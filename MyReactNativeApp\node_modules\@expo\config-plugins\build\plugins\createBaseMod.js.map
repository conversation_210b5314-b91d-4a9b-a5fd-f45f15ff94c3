{"version": 3, "file": "createBaseMod.js", "names": ["_debug", "data", "_interopRequireDefault", "require", "_withMod", "e", "__esModule", "default", "debug", "Debug", "createBaseMod", "methodName", "platform", "modName", "getFilePath", "read", "write", "isIntrospective", "withUnknown", "config", "_props", "props", "withBaseMod", "mod", "skipEmptyMod", "saveToInternal", "is<PERSON><PERSON><PERSON>", "action", "modRequest", "nextMod", "results", "filePath", "modResults", "assertModResults", "error", "message", "Object", "defineProperty", "value", "platformName", "ensuredResults", "mods", "Error", "JSON", "stringify", "upperFirst", "name", "char<PERSON>t", "toUpperCase", "slice", "createPlatformBaseMod", "provider", "withGeneratedBaseMods", "providers", "entries", "reduce", "baseMod"], "sources": ["../../src/plugins/createBaseMod.ts"], "sourcesContent": ["import Debug from 'debug';\n\nimport { BaseModOptions, withBaseMod } from './withMod';\nimport {\n  ConfigPlugin,\n  ExportedConfig,\n  ExportedConfigWithProps,\n  ModPlatform,\n} from '../Plugin.types';\n\nconst debug = Debug('expo:config-plugins:base-mods');\n\nexport type ForwardedBaseModOptions = Partial<\n  Pick<BaseModOptions, 'saveToInternal' | 'skipEmptyMod'>\n>;\n\nexport type BaseModProviderMethods<\n  ModType,\n  Props extends ForwardedBaseModOptions = ForwardedBaseModOptions,\n> = {\n  getFilePath: (config: ExportedConfigWithProps<ModType>, props: Props) => Promise<string> | string;\n  read: (\n    filePath: string,\n    config: ExportedConfigWithProps<ModType>,\n    props: Props\n  ) => Promise<ModType> | ModType;\n  write: (\n    filePath: string,\n    config: ExportedConfigWithProps<ModType>,\n    props: Props\n  ) => Promise<void> | void;\n  /**\n   * If the mod supports introspection, and avoids making any filesystem modifications during compilation.\n   * By enabling, this mod, and all of its descendants will be run in introspection mode.\n   * This should only be used for static files like JSON or XML, and not for application files that require regexes,\n   * or complex static files that require other files to be generated like Xcode `.pbxproj`.\n   */\n  isIntrospective?: boolean;\n};\n\nexport type CreateBaseModProps<\n  ModType,\n  Props extends ForwardedBaseModOptions = ForwardedBaseModOptions,\n> = {\n  methodName: string;\n  platform: ModPlatform;\n  modName: string;\n} & BaseModProviderMethods<ModType, Props>;\n\nexport function createBaseMod<\n  ModType,\n  Props extends ForwardedBaseModOptions = ForwardedBaseModOptions,\n>({\n  methodName,\n  platform,\n  modName,\n  getFilePath,\n  read,\n  write,\n  isIntrospective,\n}: CreateBaseModProps<ModType, Props>): ConfigPlugin<Props | void> {\n  const withUnknown: ConfigPlugin<Props | void> = (config, _props) => {\n    const props = _props || ({} as Props);\n    return withBaseMod<ModType>(config, {\n      platform,\n      mod: modName,\n      skipEmptyMod: props.skipEmptyMod ?? true,\n      saveToInternal: props.saveToInternal ?? false,\n      isProvider: true,\n      isIntrospective,\n      async action({ modRequest: { nextMod, ...modRequest }, ...config }) {\n        try {\n          let results: ExportedConfigWithProps<ModType> = {\n            ...config,\n            modRequest,\n          };\n\n          const filePath = await getFilePath(results, props);\n          debug(`mods.${platform}.${modName}: file path: ${filePath || '[skipped]'}`);\n          const modResults = await read(filePath, results, props);\n\n          results = await nextMod!({\n            ...results,\n            modResults,\n            modRequest,\n          });\n\n          assertModResults(results, modRequest.platform, modRequest.modName);\n\n          await write(filePath, results, props);\n          return results;\n        } catch (error: any) {\n          error.message = `[${platform}.${modName}]: ${methodName}: ${error.message}`;\n          throw error;\n        }\n      },\n    });\n  };\n\n  if (methodName) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: methodName,\n    });\n  }\n\n  return withUnknown;\n}\n\nexport function assertModResults(results: any, platformName: string, modName: string) {\n  // If the results came from a mod, they'd be in the form of [config, data].\n  // Ensure the results are an array and omit the data since it should've been written by a data provider plugin.\n  const ensuredResults = results;\n\n  // Sanity check to help locate non compliant mods.\n  if (!ensuredResults || typeof ensuredResults !== 'object' || !ensuredResults?.mods) {\n    throw new Error(\n      `Mod \\`mods.${platformName}.${modName}\\` evaluated to an object that is not a valid project config. Instead got: ${JSON.stringify(\n        ensuredResults\n      )}`\n    );\n  }\n  return ensuredResults;\n}\n\nfunction upperFirst(name: string): string {\n  return name.charAt(0).toUpperCase() + name.slice(1);\n}\n\nexport function createPlatformBaseMod<\n  ModType,\n  Props extends ForwardedBaseModOptions = ForwardedBaseModOptions,\n>({ modName, ...props }: Omit<CreateBaseModProps<ModType, Props>, 'methodName'>) {\n  // Generate the function name to ensure it's uniform and also to improve stack traces.\n  const methodName = `with${upperFirst(props.platform)}${upperFirst(modName)}BaseMod`;\n  return createBaseMod<ModType, Props>({\n    methodName,\n    modName,\n    ...props,\n  });\n}\n\n/** A TS wrapper for creating provides */\nexport function provider<ModType, Props extends ForwardedBaseModOptions = ForwardedBaseModOptions>(\n  props: BaseModProviderMethods<ModType, Props>\n) {\n  return props;\n}\n\n/** Plugin to create and append base mods from file providers */\nexport function withGeneratedBaseMods<ModName extends string>(\n  config: ExportedConfig,\n  {\n    platform,\n    providers,\n    ...props\n  }: ForwardedBaseModOptions & {\n    /** Officially supports `'ios' | 'android'` (`ModPlatform`). Arbitrary strings are supported for adding out-of-tree platforms. */\n    platform: ModPlatform & string;\n    providers: Partial<Record<ModName, BaseModProviderMethods<any, any>>>;\n  }\n): ExportedConfig {\n  return Object.entries(providers).reduce((config, [modName, value]) => {\n    const baseMod = createPlatformBaseMod({ platform, modName, ...(value as any) });\n    return baseMod(config, props);\n  }, config);\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,SAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAwD,SAAAC,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAQxD,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,+BAA+B,CAAC;AAuC7C,SAASC,aAAaA,CAG3B;EACAC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,WAAW;EACXC,IAAI;EACJC,KAAK;EACLC;AACkC,CAAC,EAA8B;EACjE,MAAMC,WAAuC,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAClE,MAAMC,KAAK,GAAGD,MAAM,IAAK,CAAC,CAAW;IACrC,OAAO,IAAAE,sBAAW,EAAUH,MAAM,EAAE;MAClCP,QAAQ;MACRW,GAAG,EAAEV,OAAO;MACZW,YAAY,EAAEH,KAAK,CAACG,YAAY,IAAI,IAAI;MACxCC,cAAc,EAAEJ,KAAK,CAACI,cAAc,IAAI,KAAK;MAC7CC,UAAU,EAAE,IAAI;MAChBT,eAAe;MACf,MAAMU,MAAMA,CAAC;QAAEC,UAAU,EAAE;UAAEC,OAAO;UAAE,GAAGD;QAAW,CAAC;QAAE,GAAGT;MAAO,CAAC,EAAE;QAClE,IAAI;UACF,IAAIW,OAAyC,GAAG;YAC9C,GAAGX,MAAM;YACTS;UACF,CAAC;UAED,MAAMG,QAAQ,GAAG,MAAMjB,WAAW,CAACgB,OAAO,EAAET,KAAK,CAAC;UAClDb,KAAK,CAAC,QAAQI,QAAQ,IAAIC,OAAO,gBAAgBkB,QAAQ,IAAI,WAAW,EAAE,CAAC;UAC3E,MAAMC,UAAU,GAAG,MAAMjB,IAAI,CAACgB,QAAQ,EAAED,OAAO,EAAET,KAAK,CAAC;UAEvDS,OAAO,GAAG,MAAMD,OAAO,CAAE;YACvB,GAAGC,OAAO;YACVE,UAAU;YACVJ;UACF,CAAC,CAAC;UAEFK,gBAAgB,CAACH,OAAO,EAAEF,UAAU,CAAChB,QAAQ,EAAEgB,UAAU,CAACf,OAAO,CAAC;UAElE,MAAMG,KAAK,CAACe,QAAQ,EAAED,OAAO,EAAET,KAAK,CAAC;UACrC,OAAOS,OAAO;QAChB,CAAC,CAAC,OAAOI,KAAU,EAAE;UACnBA,KAAK,CAACC,OAAO,GAAG,IAAIvB,QAAQ,IAAIC,OAAO,MAAMF,UAAU,KAAKuB,KAAK,CAACC,OAAO,EAAE;UAC3E,MAAMD,KAAK;QACb;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIvB,UAAU,EAAE;IACdyB,MAAM,CAACC,cAAc,CAACnB,WAAW,EAAE,MAAM,EAAE;MACzCoB,KAAK,EAAE3B;IACT,CAAC,CAAC;EACJ;EAEA,OAAOO,WAAW;AACpB;AAEO,SAASe,gBAAgBA,CAACH,OAAY,EAAES,YAAoB,EAAE1B,OAAe,EAAE;EACpF;EACA;EACA,MAAM2B,cAAc,GAAGV,OAAO;;EAE9B;EACA,IAAI,CAACU,cAAc,IAAI,OAAOA,cAAc,KAAK,QAAQ,IAAI,CAACA,cAAc,EAAEC,IAAI,EAAE;IAClF,MAAM,IAAIC,KAAK,CACb,cAAcH,YAAY,IAAI1B,OAAO,8EAA8E8B,IAAI,CAACC,SAAS,CAC/HJ,cACF,CAAC,EACH,CAAC;EACH;EACA,OAAOA,cAAc;AACvB;AAEA,SAASK,UAAUA,CAACC,IAAY,EAAU;EACxC,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;AACrD;AAEO,SAASC,qBAAqBA,CAGnC;EAAErC,OAAO;EAAE,GAAGQ;AAA8D,CAAC,EAAE;EAC/E;EACA,MAAMV,UAAU,GAAG,OAAOkC,UAAU,CAACxB,KAAK,CAACT,QAAQ,CAAC,GAAGiC,UAAU,CAAChC,OAAO,CAAC,SAAS;EACnF,OAAOH,aAAa,CAAiB;IACnCC,UAAU;IACVE,OAAO;IACP,GAAGQ;EACL,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS8B,QAAQA,CACtB9B,KAA6C,EAC7C;EACA,OAAOA,KAAK;AACd;;AAEA;AACO,SAAS+B,qBAAqBA,CACnCjC,MAAsB,EACtB;EACEP,QAAQ;EACRyC,SAAS;EACT,GAAGhC;AAKL,CAAC,EACe;EAChB,OAAOe,MAAM,CAACkB,OAAO,CAACD,SAAS,CAAC,CAACE,MAAM,CAAC,CAACpC,MAAM,EAAE,CAACN,OAAO,EAAEyB,KAAK,CAAC,KAAK;IACpE,MAAMkB,OAAO,GAAGN,qBAAqB,CAAC;MAAEtC,QAAQ;MAAEC,OAAO;MAAE,GAAIyB;IAAc,CAAC,CAAC;IAC/E,OAAOkB,OAAO,CAACrC,MAAM,EAAEE,KAAK,CAAC;EAC/B,CAAC,EAAEF,MAAM,CAAC;AACZ", "ignoreList": []}