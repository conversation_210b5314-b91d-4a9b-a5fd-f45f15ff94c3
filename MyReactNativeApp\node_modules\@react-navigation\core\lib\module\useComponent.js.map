{"version": 3, "names": ["React", "jsx", "_jsx", "NavigationContent", "render", "children", "useComponent", "renderRef", "useRef", "current", "useEffect", "Error"], "sourceRoot": "../../src", "sources": ["useComponent.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAS/B,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAgB,CAAC,KAAK;EACzD,OAAOD,MAAM,CAACC,QAAQ,CAAC;AACzB,CAAC;AAED,OAAO,SAASC,YAAYA,CAACF,MAAc,EAAE;EAC3C,MAAMG,SAAS,GAAGP,KAAK,CAACQ,MAAM,CAAgBJ,MAAM,CAAC;;EAErD;EACA;EACA;EACAG,SAAS,CAACE,OAAO,GAAGL,MAAM;EAE1BJ,KAAK,CAACU,SAAS,CAAC,MAAM;IACpBH,SAAS,CAACE,OAAO,GAAG,IAAI;EAC1B,CAAC,CAAC;EAEF,OAAOT,KAAK,CAACQ,MAAM,CAAC,CAAC;IAAEH;EAAwC,CAAC,KAAK;IACnE,MAAMD,MAAM,GAAGG,SAAS,CAACE,OAAO;IAEhC,IAAIL,MAAM,KAAK,IAAI,EAAE;MACnB,MAAM,IAAIO,KAAK,CACb,+EACF,CAAC;IACH;IAEA,oBAAOT,IAAA,CAACC,iBAAiB;MAACC,MAAM,EAAEA,MAAO;MAAAC,QAAA,EAAEA;IAAQ,CAAoB,CAAC;EAC1E,CAAC,CAAC,CAACI,OAAO;AACZ", "ignoreList": []}