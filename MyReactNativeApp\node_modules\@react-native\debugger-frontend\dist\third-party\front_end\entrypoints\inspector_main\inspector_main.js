import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as a from"../../core/i18n/i18n.js";import*as s from"../../ui/legacy/legacy.js";import*as n from"../../ui/visual_logging/visual_logging.js";import*as r from"../../core/root/root.js";import*as o from"../../core/sdk/sdk.js";import*as i from"../../panels/mobile_throttling/mobile_throttling.js";import*as l from"../../ui/legacy/components/utils/utils.js";import*as d from"../../core/platform/platform.js";import*as c from"../../models/bindings/bindings.js";const g=new CSSStyleSheet;g.replaceSync(':host{padding:12px}[is="dt-checkbox"]{margin:0 0 10px;flex:none}.panel-section-separator{height:1px;margin-bottom:10px;background:var(--sys-color-divider);flex:none}.panel-section-separator:last-child{background:transparent}.chrome-select-label{margin-bottom:16px}\n/*# sourceURL=renderingOptions.css */\n');const h={paintFlashing:"Paint flashing",highlightsAreasOfThePageGreen:"Highlights areas of the page (green) that need to be repainted. May not be suitable for people prone to photosensitive epilepsy.",layoutShiftRegions:"Layout Shift Regions",highlightsAreasOfThePageBlueThat:"Highlights areas of the page (blue) that were shifted. May not be suitable for people prone to photosensitive epilepsy.",layerBorders:"Layer borders",showsLayerBordersOrangeoliveAnd:"Shows layer borders (orange/olive) and tiles (cyan).",frameRenderingStats:"Frame Rendering Stats",plotsFrameThroughputDropped:"Plots frame throughput, dropped frames distribution, and GPU memory.",scrollingPerformanceIssues:"Scrolling performance issues",highlightsElementsTealThatCan:"Highlights elements (teal) that can slow down scrolling, including touch & wheel event handlers and other main-thread scrolling situations.",highlightAdFrames:"Highlight ad frames",highlightsFramesRedDetectedToBe:"Highlights frames (red) detected to be ads.",coreWebVitals:"Core Web Vitals",showsAnOverlayWithCoreWebVitals:"Shows an overlay with Core Web Vitals.",disableLocalFonts:"Disable local fonts",disablesLocalSourcesInFontface:"Disables `local()` sources in `@font-face` rules. Requires a page reload to apply.",emulateAFocusedPage:"Emulate a focused page",emulatesAFocusedPage:"Keep page focused. Commonly used for debugging disappearing elements.",emulateAutoDarkMode:"Enable automatic dark mode",emulatesAutoDarkMode:"Enables automatic dark mode and sets `prefers-color-scheme` to `dark`.",forcesMediaTypeForTestingPrint:"Forces media type for testing print and screen styles",forcesCssPreferscolorschemeMedia:"Forces CSS `prefers-color-scheme` media feature",forcesCssPrefersreducedmotion:"Forces CSS `prefers-reduced-motion` media feature",forcesCssPreferscontrastMedia:"Forces CSS `prefers-contrast` media feature",forcesCssPrefersreduceddataMedia:"Forces CSS `prefers-reduced-data` media feature",forcesCssPrefersreducedtransparencyMedia:"Forces CSS `prefers-reduced-transparency` media feature",forcesCssColorgamutMediaFeature:"Forces CSS `color-gamut` media feature",forcesVisionDeficiencyEmulation:"Forces vision deficiency emulation",disableAvifImageFormat:"Disable `AVIF` image format",requiresAPageReloadToApplyAnd:"Requires a page reload to apply and disables caching for image requests.",disableWebpImageFormat:"Disable `WebP` image format",forcesCssForcedColors:"Forces CSS forced-colors media feature"},u=a.i18n.registerUIStrings("entrypoints/inspector_main/RenderingOptions.ts",h),p=a.i18n.getLocalizedString.bind(void 0,u);class m extends s.Widget.VBox{constructor(){super(!0),this.element.setAttribute("jslog",`${n.panel("rendering").track({resize:!0})}`),this.#e(p(h.paintFlashing),p(h.highlightsAreasOfThePageGreen),e.Settings.Settings.instance().moduleSetting("show-paint-rects")),this.#e(p(h.layoutShiftRegions),p(h.highlightsAreasOfThePageBlueThat),e.Settings.Settings.instance().moduleSetting("show-layout-shift-regions")),this.#e(p(h.layerBorders),p(h.showsLayerBordersOrangeoliveAnd),e.Settings.Settings.instance().moduleSetting("show-debug-borders")),this.#e(p(h.frameRenderingStats),p(h.plotsFrameThroughputDropped),e.Settings.Settings.instance().moduleSetting("show-fps-counter")),this.#e(p(h.scrollingPerformanceIssues),p(h.highlightsElementsTealThatCan),e.Settings.Settings.instance().moduleSetting("show-scroll-bottleneck-rects")),this.#e(p(h.highlightAdFrames),p(h.highlightsFramesRedDetectedToBe),e.Settings.Settings.instance().moduleSetting("show-ad-highlights")),this.#e(p(h.coreWebVitals),p(h.showsAnOverlayWithCoreWebVitals),e.Settings.Settings.instance().moduleSetting("show-web-vitals"),{toggle:t.UserMetrics.Action.ToggleShowWebVitals}),this.#e(p(h.disableLocalFonts),p(h.disablesLocalSourcesInFontface),e.Settings.Settings.instance().moduleSetting("local-fonts-disabled")),this.#e(p(h.emulateAFocusedPage),p(h.emulatesAFocusedPage),e.Settings.Settings.instance().moduleSetting("emulate-page-focus"),{toggle:t.UserMetrics.Action.ToggleEmulateFocusedPageFromRenderingTab}),this.#e(p(h.emulateAutoDarkMode),p(h.emulatesAutoDarkMode),e.Settings.Settings.instance().moduleSetting("emulate-auto-dark-mode")),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.#t(p(h.forcesCssPreferscolorschemeMedia),e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-color-scheme")),this.#t(p(h.forcesMediaTypeForTestingPrint),e.Settings.Settings.instance().moduleSetting("emulated-css-media")),this.#t(p(h.forcesCssForcedColors),e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-forced-colors")),window.matchMedia("not all and (prefers-contrast), (prefers-contrast)").matches&&this.#t(p(h.forcesCssPreferscontrastMedia),e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-contrast")),this.#t(p(h.forcesCssPrefersreducedmotion),e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-motion")),window.matchMedia("not all and (prefers-reduced-data), (prefers-reduced-data)").matches&&this.#t(p(h.forcesCssPrefersreduceddataMedia),e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-data")),window.matchMedia("not all and (prefers-reduced-transparency), (prefers-reduced-transparency)").matches&&this.#t(p(h.forcesCssPrefersreducedtransparencyMedia),e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-transparency")),this.#t(p(h.forcesCssColorgamutMediaFeature),e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-color-gamut")),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.#t(p(h.forcesVisionDeficiencyEmulation),e.Settings.Settings.instance().moduleSetting("emulated-vision-deficiency")),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.#e(p(h.disableAvifImageFormat),p(h.requiresAPageReloadToApplyAnd),e.Settings.Settings.instance().moduleSetting("avif-format-disabled")),this.#e(p(h.disableWebpImageFormat),p(h.requiresAPageReloadToApplyAnd),e.Settings.Settings.instance().moduleSetting("webp-format-disabled")),this.contentElement.createChild("div").classList.add("panel-section-separator")}#e(e,t,a,n){const r=s.UIUtils.CheckboxLabel.create(e,!1,t,a.name);return s.SettingsUI.bindCheckbox(r.checkboxElement,a,n),this.contentElement.appendChild(r),r}#t(e,t){const a=s.SettingsUI.createControlForSetting(t,e);a&&this.contentElement.appendChild(a)}wasShown(){super.wasShown(),this.registerCSSFiles([g])}}var S=Object.freeze({__proto__:null,RenderingOptionsView:m,ReloadActionDelegate:class{handleAction(t,a){const s=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-color-scheme");if("rendering.toggle-prefers-color-scheme"===a){const e=["","light","dark"],t=e.findIndex((e=>e===s.get()||""));return s.set(e[(t+1)%3]),!0}return!1}}});const f=new CSSStyleSheet;f.replaceSync(".node-icon{width:28px;height:26px;background-image:var(--image-file-nodeIcon);background-size:17px 17px;background-repeat:no-repeat;background-position:center;opacity:80%;cursor:auto}.node-icon:hover{opacity:100%}.node-icon.inactive{filter:grayscale(100%)}\n/*# sourceURL=nodeIcon.css */\n");const b={main:"Main",tab:"Tab",javascriptIsDisabled:"JavaScript is disabled",openDedicatedTools:"Open dedicated DevTools for `Node.js`"},T=a.i18n.registerUIStrings("entrypoints/inspector_main/InspectorMain.ts",b),C=a.i18n.getLocalizedString.bind(void 0,T);let y,w;class I{static instance(e={forceNew:null}){const{forceNew:t}=e;return y&&!t||(y=new I),y}async run(){let e=!0;await o.Connections.initMainConnection((async()=>{const t=r.Runtime.Runtime.queryParam("v8only")?o.Target.Type.Node:"tab"===r.Runtime.Runtime.queryParam("targetType")?o.Target.Type.Tab:o.Target.Type.Frame,a=t===o.Target.Type.Frame&&"sources"===r.Runtime.Runtime.queryParam("panel"),s=t===o.Target.Type.Frame?C(b.main):C(b.tab),n=o.TargetManager.TargetManager.instance().createTarget("main",s,t,null,void 0,a);if(await new Promise((e=>{const t=o.TargetManager.TargetManager.instance();t.observeTargets({targetAdded:a=>{a===t.primaryPageTarget()&&(a.setName(C(b.main)),e(a))},targetRemoved:e=>{}})})),e){if(e=!1,a){const e=n.model(o.DebuggerModel.DebuggerModel);e&&(e.isReadyToPause()||await e.once(o.DebuggerModel.Events.DebuggerIsReadyToPause),e.pause())}t!==o.Target.Type.Tab&&n.runtimeAgent().invoke_runIfWaitingForDebugger()}}),l.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost),new F,new A,new i.NetworkPanelIndicator.NetworkPanelIndicator,t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.ReloadInspectedPage,(({data:e})=>{o.ResourceTreeModel.ResourceTreeModel.reloadAllPages(e)}))}}e.Runnable.registerEarlyInitializationRunnable(I.instance);class v{#a;#s;constructor(){const e=document.createElement("div"),a=s.UIUtils.createShadowRootWithCoreStyles(e,{cssFile:[f],delegatesFocus:void 0});this.#a=a.createChild("div","node-icon"),e.addEventListener("click",(()=>t.InspectorFrontendHost.InspectorFrontendHostInstance.openNodeFrontend()),!1),this.#s=new s.Toolbar.ToolbarItem(e),this.#s.setTitle(C(b.openDedicatedTools)),o.TargetManager.TargetManager.instance().addEventListener("AvailableTargetsChanged",(e=>this.#n(e.data))),this.#s.setVisible(!1),this.#n([])}static instance(e={forceNew:null}){const{forceNew:t}=e;return w&&!t||(w=new v),w}#n(e){const t=Boolean(e.find((e=>"node"===e.type&&!e.attached)));this.#a.classList.toggle("inactive",!t),t&&this.#s.setVisible(!0)}item(){return this.#s}}class F{constructor(){function t(){const t=[];e.Settings.Settings.instance().moduleSetting("java-script-disabled").get()&&t.push(C(b.javascriptIsDisabled)),s.InspectorView.InspectorView.instance().setPanelWarnings("sources",t)}e.Settings.Settings.instance().moduleSetting("java-script-disabled").addChangeListener(t),t()}}class A{#r;#o;#i;constructor(){this.#r=e.Settings.Settings.instance().moduleSetting("auto-attach-to-created-pages"),this.#r.addChangeListener(this.#l,this),this.#l(),this.#o=e.Settings.Settings.instance().moduleSetting("network.ad-blocking-enabled"),this.#o.addChangeListener(this.#n,this),this.#i=e.Settings.Settings.instance().moduleSetting("emulate-page-focus"),this.#i.addChangeListener(this.#n,this),o.TargetManager.TargetManager.instance().observeTargets(this)}#d(e){e.type()===o.Target.Type.Frame&&e.parentTarget()?.type()!==o.Target.Type.Frame&&(e.pageAgent().invoke_setAdBlockingEnabled({enabled:this.#o.get()}),e.emulationAgent().invoke_setFocusEmulationEnabled({enabled:this.#i.get()}))}#l(){t.InspectorFrontendHost.InspectorFrontendHostInstance.setOpenNewWindowForPopups(this.#r.get())}#n(){for(const e of o.TargetManager.TargetManager.instance().targets())this.#d(e)}targetAdded(e){this.#d(e)}targetRemoved(e){}}o.ChildTargetManager.ChildTargetManager.install();var M=Object.freeze({__proto__:null,InspectorMainImpl:I,ReloadActionDelegate:class{handleAction(e,t){if(r.Runtime.experiments.isEnabled("react-native-specific-ui"))switch(t){case"inspector-main.reload":case"inspector-main.hard-reload":const e=o.TargetManager.TargetManager.instance().primaryPageTarget();return!!e&&(e.pageAgent().invoke_reload({ignoreCache:!0}),!0)}switch(t){case"inspector-main.reload":return o.ResourceTreeModel.ResourceTreeModel.reloadAllPages(!1),!0;case"inspector-main.hard-reload":return o.ResourceTreeModel.ResourceTreeModel.reloadAllPages(!0),!0}return!1}},FocusDebuggeeActionDelegate:class{handleAction(e,t){const a=o.TargetManager.TargetManager.instance().primaryPageTarget();return!!a&&(a.pageAgent().invoke_bringToFront(),!0)}},NodeIndicator:v,SourcesPanelIndicator:F,BackendSettingsSync:A});const x=new CSSStyleSheet;x.replaceSync(":host{padding:2px 1px 2px 2px;white-space:nowrap;display:flex;flex-direction:column;height:36px;justify-content:center;overflow-y:auto}.title{overflow:hidden;padding-left:8px;text-overflow:ellipsis;flex-grow:0}.subtitle{color:var(--sys-color-token-subtle);margin-right:3px;overflow:hidden;padding-left:8px;text-overflow:ellipsis;flex-grow:0}:host(.highlighted) .subtitle{color:inherit}\n/*# sourceURL=outermostTargetSelector.css */\n");const P={targetNotSelected:"Page: Not selected",targetS:"Page: {PH1}"},k=a.i18n.registerUIStrings("entrypoints/inspector_main/OutermostTargetSelector.ts",P),R=a.i18n.getLocalizedString.bind(void 0,k);let D;class L{listItems;#c;#g;constructor(){this.listItems=new s.ListModel.ListModel,this.#c=new s.SoftDropDown.SoftDropDown(this.listItems,this),this.#c.setRowHeight(36),this.#g=new s.Toolbar.ToolbarItem(this.#c.element),this.#g.setTitle(R(P.targetNotSelected)),this.listItems.addEventListener("ItemsReplaced",(()=>this.#g.setEnabled(Boolean(this.listItems.length)))),this.#g.element.classList.add("toolbar-has-dropdown");const e=o.TargetManager.TargetManager.instance();e.addModelListener(o.ChildTargetManager.ChildTargetManager,"TargetInfoChanged",this.#h,this),e.addEventListener("NameChanged",this.#u,this),e.observeTargets(this),s.Context.Context.instance().addFlavorChangeListener(o.Target.Target,this.#p,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return D&&!t||(D=new L),D}item(){return this.#g}highlightedItemChanged(e,t,a,s){a&&a.classList.remove("highlighted"),s&&s.classList.add("highlighted")}titleFor(e){return e.name()}targetAdded(e){e.outermostTarget()===e&&(this.listItems.insertWithComparator(e,this.#m()),this.#g.setVisible(this.listItems.length>1),e===s.Context.Context.instance().flavor(o.Target.Target)&&this.#c.selectItem(e))}targetRemoved(e){const t=this.listItems.indexOf(e);-1!==t&&(this.listItems.remove(t),this.#g.setVisible(this.listItems.length>1))}#m(){return(e,t)=>{const a=e.targetInfo(),s=t.targetInfo();return a&&s?!a.subtype?.length&&s.subtype?.length?-1:a.subtype?.length&&!s.subtype?.length?1:a.url.localeCompare(s.url):0}}#h(e){const t=o.TargetManager.TargetManager.instance().targetById(e.data.targetId);t&&t.outermostTarget()===t&&(this.targetRemoved(t),this.targetAdded(t))}#u(e){const t=e.data;t&&t.outermostTarget()===t&&(this.targetRemoved(t),this.targetAdded(t))}#p({data:e}){this.#c.selectItem(e?.outermostTarget()||null)}createElementForItem(e){const t=document.createElement("div");t.classList.add("target");const a=s.UIUtils.createShadowRootWithCoreStyles(t,{cssFile:[x],delegatesFocus:void 0}),n=a.createChild("div","title");s.UIUtils.createTextChild(n,d.StringUtilities.trimEndWithMaxLength(this.titleFor(e),100));const r=a.createChild("div","subtitle");return s.UIUtils.createTextChild(r,this.#S(e)),t}#S(e){const t=e.targetInfo();return e===o.TargetManager.TargetManager.instance().primaryPageTarget()&&t?c.ResourceUtils.displayNameForURL(t.url):e.targetInfo()?.subtype||""}isItemSelectable(e){return!0}itemSelected(e){const t=e?R(P.targetS,{PH1:this.titleFor(e)}):R(P.targetNotSelected);this.#g.setTitle(t),e&&e!==s.Context.Context.instance().flavor(o.Target.Target)?.outermostTarget()&&s.Context.Context.instance().setFlavor(o.Target.Target,e)}}var E=Object.freeze({__proto__:null,OutermostTargetSelector:L});export{M as InspectorMain,E as OutermostTargetSelector,S as RenderingOptions};
