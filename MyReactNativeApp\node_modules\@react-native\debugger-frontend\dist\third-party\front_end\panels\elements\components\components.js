import*as e from"../../../core/i18n/i18n.js";import*as t from"../../../core/platform/platform.js";import*as o from"../../../ui/components/render_coordinator/render_coordinator.js";import*as n from"../../../ui/lit-html/lit-html.js";import*as r from"../../../ui/components/buttons/buttons.js";import*as i from"../../../ui/components/input/input.js";import*as s from"../../../ui/visual_logging/visual_logging.js";import*as a from"../../../ui/components/icon_button/icon_button.js";import*as l from"../../../ui/legacy/components/inline_editor/inline_editor.js";import*as c from"../../../ui/legacy/legacy.js";import*as d from"../../../core/common/common.js";import*as p from"../../../ui/components/helpers/helpers.js";import*as u from"../../../ui/components/node_text/node_text.js";import*as h from"../../../core/sdk/sdk.js";import*as g from"../../../ui/components/legacy_wrapper/legacy_wrapper.js";const m=new CSSStyleSheet;m.replaceSync(".container{width:100%;display:inline-block}.container:hover{background-color:var(--sys-color-state-hover-on-subtle)}span{color:var(--sys-color-token-meta);font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)}.role-value{color:var(--sys-color-token-tag)}.attribute-name{color:var(--sys-color-token-attribute)}.attribute-value{color:var(--sys-color-token-attribute-value)}\n/*# sourceURL=accessibilityTreeNode.css */\n");const y={ignored:"Ignored"},v=e.i18n.registerUIStrings("panels/elements/components/AccessibilityTreeNode.ts",y),f=e.i18n.getLocalizedString.bind(void 0,v);class b extends HTMLElement{static litTagName=n.literal`devtools-accessibility-tree-node`;#e=this.attachShadow({mode:"open"});#t=!0;#o="";#n="";#r=[];#i="";set data(e){this.#t=e.ignored,this.#o=e.name,this.#n=e.role,this.#r=e.properties,this.#i=e.id,this.#s()}connectedCallback(){this.#e.adoptedStyleSheets=[m]}async#s(){const e=n.html`<span class='role-value'>${r=this.#n,r.length>1e4?t.StringUtilities.trimMiddle(r,1e4):r}</span>`;var r;const i=n.html`"<span class='attribute-value'>${this.#o}</span>"`,s=this.#r.map((({name:e,value:t})=>function(e){switch(e){case"boolean":case"booleanOrUndefined":case"string":case"number":return!0;default:return!1}}(t.type)?n.html` <span class='attribute-name'>${e}</span>:&nbsp;<span class='attribute-value'>${t.value}</span>`:n.nothing)),a=this.#t?n.html`<span>${f(y.ignored)}</span>`:n.html`${e}&nbsp;${i}${s}`;await o.RenderCoordinator.RenderCoordinator.instance().write(`Accessibility node ${this.#i} render`,(()=>{n.render(n.html`<div class='container'>${a}</div>`,this.#e,{host:this})}))}}customElements.define("devtools-accessibility-tree-node",b);var x,w=Object.freeze({__proto__:null,AccessibilityTreeNode:b});function S(e){switch(e){case x.GRID:return{name:"grid",category:"Layout",enabledByDefault:!0};case x.SUBGRID:return{name:"subgrid",category:"Layout",enabledByDefault:!0};case x.FLEX:return{name:"flex",category:"Layout",enabledByDefault:!0};case x.AD:return{name:"ad",category:"Security",enabledByDefault:!0};case x.SCROLL_SNAP:return{name:"scroll-snap",category:"Layout",enabledByDefault:!0};case x.CONTAINER:return{name:"container",category:"Layout",enabledByDefault:!0};case x.SLOT:return{name:"slot",category:"Layout",enabledByDefault:!0};case x.TOP_LAYER:return{name:"top-layer",category:"Layout",enabledByDefault:!0};case x.REVEAL:return{name:"reveal",category:"Default",enabledByDefault:!0};case x.MEDIA:return{name:"media",category:"Default",enabledByDefault:!1}}}let k;function $(e){if(!k){k=new Map;for(const{name:e,category:t}of Object.values(x).map(S))k.set(e,t)}return k.get(e)||"Default"}!function(e){e.GRID="grid",e.SUBGRID="subgrid",e.FLEX="flex",e.AD="ad",e.SCROLL_SNAP="scroll-snap",e.CONTAINER="container",e.SLOT="slot",e.TOP_LAYER="top-layer",e.REVEAL="reveal",e.MEDIA="media"}(x||(x={}));const N=Object.values(x).map(S).map((({name:e,enabledByDefault:t})=>({adorner:e,isEnabled:t})));const C=new Map(["Security","Layout","Default"].map(((e,t)=>[e,t+1])));var E=Object.freeze({__proto__:null,get RegisteredAdorners(){return x},getRegisteredAdorner:S,DefaultAdornerSettings:N,AdornerManager:class{#a=new Map;#l;constructor(e){this.#l=e,this.#c()}updateSettings(e){this.#a=e,this.#d()}getSettings(){return this.#a}isAdornerEnabled(e){return this.#a.get(e)||!1}#d(){const e=[];for(const[t,o]of this.#a)e.push({adorner:t,isEnabled:o});this.#l.set(e)}#p(){const e=this.#l.get();for(const t of e)this.#a.set(t.adorner,t.isEnabled)}#c(){this.#p();const e=new Set(this.#a.keys());for(const{adorner:t,isEnabled:o}of N)e.delete(t),this.#a.has(t)||this.#a.set(t,o);for(const t of e)this.#a.delete(t);this.#d()}},AdornerCategoryOrder:C,compareAdornerNamesByCategory:function(e,t){return(C.get($(e))||Number.POSITIVE_INFINITY)-(C.get($(t))||Number.POSITIVE_INFINITY)}});const M=new CSSStyleSheet;M.replaceSync(".adorner-settings-pane{display:flex;height:auto;padding:6px 12px;color:var(--sys-color-on-surface);font-size:12px;align-items:center}.settings-title{font-weight:500;margin-right:6px}.setting{margin-left:1em}.adorner-status{margin:auto 0.4em auto 0}.adorner-status,\n.adorner-name{vertical-align:middle}\n/*# sourceURL=adornerSettingsPane.css */\n");const T={settingsTitle:"Show badges",closeButton:"Close"},L=e.i18n.registerUIStrings("panels/elements/components/AdornerSettingsPane.ts",T),z=e.i18n.getLocalizedString.bind(void 0,L),{render:O,html:D}=n;class j extends Event{static eventName="adornersettingupdated";data;constructor(e,t,o){super(j.eventName,{}),this.data={adornerName:e,isEnabledNow:t,newSettings:o}}}class I extends HTMLElement{static litTagName=n.literal`devtools-adorner-settings-pane`;#e=this.attachShadow({mode:"open"});#u=new Map;connectedCallback(){this.#e.adoptedStyleSheets=[i.checkboxStyles,M]}set data(e){this.#u=new Map(e.settings.entries()),this.#s()}show(){this.classList.remove("hidden");const e=this.#e.querySelector(".adorner-settings-pane");e&&e.focus()}hide(){this.classList.add("hidden")}#h(e){const t=e.target,o=t.dataset.adorner;if(void 0===o)return;const n=t.checked;this.#u.set(o,n),this.dispatchEvent(new j(o,n,this.#u)),this.#s()}#s(){const e=[];for(const[t,o]of this.#u)e.push(D`
        <label class="setting" title=${t}>
          <input
            class="adorner-status"
            type="checkbox" name=${t}
            .checked=${o}
            jslog=${s.toggle(t).track({change:!0})}
            data-adorner=${t}>
          <span class="adorner-name">${t}</span>
        </label>
      `);O(D`
      <div class="adorner-settings-pane" tabindex="-1" jslog=${s.pane("adorner-settings")}>
        <div class="settings-title">${z(T.settingsTitle)}</div>
        <div class="setting-list" @change=${this.#h}>
          ${e}
        </div>
        <${r.Button.Button.litTagName} aria-label=${z(T.closeButton)}
                                             .iconName=${"cross"}
                                             .size=${"SMALL"}
                                             .title=${z(T.closeButton)}
                                             .variant=${"icon"}
                                             jslog=${s.close().track({click:!0})}
                                             @click=${this.hide}></${r.Button.Button.litTagName}>
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-adorner-settings-pane",I);var _=Object.freeze({__proto__:null,AdornerSettingUpdatedEvent:j,AdornerSettingsPane:I});const P=new CSSStyleSheet;P.replaceSync(".icon-link{color:var(--text-link);width:13px;height:13px;&:hover{cursor:pointer}}\n/*# sourceURL=anchorFunctionLinkSwatch.css */\n");const A={jumpToAnchorNode:"Jump to anchor node"},B=e.i18n.registerUIStrings("panels/elements/components/AnchorFunctionLinkSwatch.ts",A),H=e.i18n.getLocalizedString.bind(void 0,B),{render:R,html:q}=n;class V extends HTMLElement{static litTagName=n.literal`devtools-anchor-function-link-swatch`;#e=this.attachShadow({mode:"open"});#g;constructor(e){super(),this.#g=e}dataForTest(){return this.#g}connectedCallback(){this.#e.adoptedStyleSheets=[P],this.render()}set data(e){this.#g=e,this.render()}#m(e){e.stopPropagation(),this.#g.onLinkActivate()}#y(){return q`<${l.LinkSwatch.LinkSwatch.litTagName}
      @mouseenter=${this.#g.onMouseEnter}
      @mouseleave=${this.#g.onMouseLeave}
      .data=${{text:this.#g.identifier,isDefined:Boolean(this.#g.anchorNode),jslogContext:"anchor-link",onLinkActivate:this.#g.onLinkActivate}}></${l.LinkSwatch.LinkSwatch.litTagName}>`}#v(){return q`<${a.Icon.Icon.litTagName}
      role='button'
      title=${H(A.jumpToAnchorNode)}
      class='icon-link'
      name='open-externally'
      jslog=${s.action("jump-to-anchor-node").track({click:!0})}
      @mouseenter=${this.#g.onMouseEnter}
      @mouseleave=${this.#g.onMouseLeave}
      @mousedown=${e=>e.stopPropagation()}
      @click=${this.#m}
    ></${a.Icon.Icon.litTagName}>`}render(){(this.#g.identifier||this.#g.anchorNode)&&(this.#g.identifier?R(q`${this.#y()}${this.#g.needsSpace?" ":""}`,this.#e,{host:this}):R(q`${this.#v()}${this.#g.needsSpace?" ":""}`,this.#e,{host:this}))}}customElements.define("devtools-anchor-function-link-swatch",V);var F=Object.freeze({__proto__:null,AnchorFunctionLinkSwatch:V});const U=new CSSStyleSheet;U.replaceSync(":host{position:relative;overflow:hidden;flex:auto;text-overflow:ellipsis}.computed-style-property{--goto-size:16px;font-family:var(--monospace-font-family);font-size:var(--monospace-font-size);min-height:16px;box-sizing:border-box;padding-top:2px;white-space:nowrap;user-select:text}.computed-style-property:hover{background-color:var(--sys-color-state-hover-on-subtle);cursor:text}.computed-style-property.inherited{opacity:50%}.property-name,\n.property-value{display:contents;overflow:hidden;text-overflow:ellipsis}.property-name{width:16em;max-width:52%;margin-right:calc(var(--goto-size) / 2);display:inline-block;vertical-align:text-top;color:var(--webkit-css-property-color,var(--sys-color-token-property-special))}.property-value{margin-left:2em}.goto{display:none;cursor:pointer;position:absolute;width:var(--goto-size);height:var(--goto-size);margin:-1px 0 0 calc(-1 * var(--goto-size));mask:var(--image-file-goto-filled) center /contain no-repeat;background-color:var(--sys-color-primary-bright)}.computed-style-property:hover .goto{display:inline-block}.hidden{display:none}:host-context(.computed-narrow) .computed-style-property{white-space:normal;& .goto{display:none;margin-left:0}}:host-context(.computed-narrow) .property-name,\n:host-context(.computed-narrow) .property-value{display:inline-block;width:100%;max-width:100%;margin-left:0;white-space:nowrap}:host-context(.computed-narrow) .computed-style-property:not(.inherited):hover{& .property-value{margin-left:var(--goto-size)}& .goto{display:inline-block}}@media (forced-colors: active){.computed-style-property.inherited{opacity:100%}.computed-style-property:hover{forced-color-adjust:none;background-color:Highlight}.computed-style-property:hover *{color:HighlightText}.goto{background-color:HighlightText}}\n/*# sourceURL=computedStyleProperty.css */\n");const{render:G,html:W}=n;class Q extends Event{static eventName="onnavigatetosource";constructor(){super(Q.eventName,{bubbles:!0,composed:!0})}}class Y extends HTMLElement{static litTagName=n.literal`devtools-computed-style-property`;#e=this.attachShadow({mode:"open"});#f=!1;#b=!1;connectedCallback(){this.#e.adoptedStyleSheets=[U],this.#s()}set inherited(e){e!==this.#f&&(this.#f=e,this.#s())}set traceable(e){e!==this.#b&&(this.#b=e,this.#s())}#x(){this.dispatchEvent(new Q)}#s(){G(W`
      <div class="computed-style-property ${this.#f?"inherited":""}">
        <div class="property-name">
          <slot name="name"></slot>
        </div>
        <span class="hidden" aria-hidden="false">: </span>
        ${this.#b?W`<span class="goto" @click=${this.#x} jslog=${s.action("elements.jump-to-style").track({click:!0})}></span>`:null}
        <div class="property-value">
          <slot name="value"></slot>
        </div>
        <span class="hidden" aria-hidden="false">;</span>
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-computed-style-property",Y);var X=Object.freeze({__proto__:null,NavigateToSourceEvent:Q,ComputedStyleProperty:Y});const K=new CSSStyleSheet;K.replaceSync(':host{text-overflow:ellipsis;overflow:hidden;flex-grow:1}.computed-style-trace{margin-left:16px;font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)}.computed-style-trace:hover{background-color:var(--sys-color-state-hover-on-subtle);cursor:text}.goto{--size:16px;display:none;cursor:pointer;position:absolute;width:var(--size);height:var(--size);margin:-1px 0 0 calc(-1 * var(--size));mask:var(--image-file-goto-filled) center /contain no-repeat;background-color:var(--sys-color-surface-variant)}.computed-style-trace:hover .goto{display:inline-block}.devtools-link{color:var(--sys-color-on-surface);text-decoration-color:var(--sys-color-token-subtle);text-decoration-line:underline;cursor:pointer}.trace-value{margin-left:16px}.computed-style-trace.inactive slot[name="trace-value"]{text-decoration:line-through}.trace-selector{--override-trace-selector-color:var(--sys-color-neutral-bright);color:var(--override-trace-selector-color);padding-left:2em}.trace-link{user-select:none;float:right;padding-left:1em;position:relative;z-index:1}@media (forced-colors: active){.computed-style-trace:hover{forced-color-adjust:none;background-color:Highlight}.goto{background-color:Highlight}.computed-style-trace:hover *{color:HighlightText}.computed-style-trace:hover .trace-selector{--override-trace-selector-color:HighlightText}}\n/*# sourceURL=computedStyleTrace.css */\n');const{render:J,html:Z}=n;class ee extends HTMLElement{static litTagName=n.literal`devtools-computed-style-trace`;#e=this.attachShadow({mode:"open"});#w="";#S=!1;#k=()=>{};#$;connectedCallback(){this.#e.adoptedStyleSheets=[K],c.UIUtils.injectCoreStyles(this.#e)}set data(e){this.#w=e.selector,this.#S=e.active,this.#k=e.onNavigateToSource,this.#$=e.ruleOriginNode,this.#s()}#s(){J(Z`
      <div class="computed-style-trace ${this.#S?"active":"inactive"}">
        <span class="goto" @click=${this.#k}></span>
        <slot name="trace-value" @click=${this.#k}></slot>
        <span class="trace-selector">${this.#w}</span>
        <span class="trace-link">${this.#$}</span>
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-computed-style-trace",ee);var te=Object.freeze({__proto__:null,ComputedStyleTrace:ee});const oe=new CSSStyleSheet;oe.replaceSync(":host{padding:6px}.hint-popup-wrapper{max-width:232px;font-size:12px;line-height:1.4}code{font-weight:bold;font-family:inherit}.hint-popup-possible-fix{margin-top:8px}.clickable{color:var(--sys-color-primary)}.underlined{text-decoration:underline}.unbreakable-text{white-space:nowrap}\n/*# sourceURL=cssHintDetailsView.css */\n");const ne={learnMore:"Learn More"},re=e.i18n.registerUIStrings("panels/elements/components/CSSHintDetailsView.ts",ne),ie=e.i18n.getLocalizedString.bind(void 0,re),{render:se,html:ae,Directives:le}=n;class ce extends HTMLElement{static litTagName=n.literal`devtools-css-hint-details-view`;#e=this.attachShadow({mode:"open"});#N;constructor(e){super(),this.#N=e,this.#e.adoptedStyleSheets=[oe],this.#s()}#s(){const e=this.#N.getLearnMoreLink();se(ae`
        <div class="hint-popup-wrapper">
          <div class="hint-popup-reason">
            ${le.unsafeHTML(this.#N.getMessage())}
          </div>
          ${this.#N.getPossibleFixMessage()?ae`
              <div class="hint-popup-possible-fix">
                  ${le.unsafeHTML(this.#N.getPossibleFixMessage())}
                  ${e?ae`
                      <x-link id="learn-more" href=${e} class="clickable underlined unbreakable-text"}>
                          ${ie(ne.learnMore)}
                      </x-link>
                  `:""}
              </div>
          `:""}
        </div>
      `,this.#e,{host:this})}}customElements.define("devtools-css-hint-details-view",ce);var de=Object.freeze({__proto__:null,CSSHintDetailsView:ce});const pe=new CSSStyleSheet;pe.replaceSync(":host{padding:6px}.docs-popup-wrapper{max-width:350px;font-size:12px;line-height:1.4}.docs-popup-section{margin-top:8px}.clickable{color:var(--sys-color-primary)}.underlined{text-decoration:underline}.unbreakable-text{white-space:nowrap}.footer{display:flex;justify-content:space-between}.dont-show input{vertical-align:bottom}\n/*# sourceURL=cssPropertyDocsView.css */\n");const ue={learnMore:"Learn more",dontShow:"Don't show"},he=e.i18n.registerUIStrings("panels/elements/components/CSSPropertyDocsView.ts",ue),ge=e.i18n.getLocalizedString.bind(void 0,he),{render:me,html:ye}=n;class ve extends HTMLElement{static litTagName=n.literal`devtools-css-property-docs-view`;#e=this.attachShadow({mode:"open"});#C;constructor(e){super(),this.#C=e,this.#e.adoptedStyleSheets=[i.checkboxStyles,pe],this.#s()}#E(e){const t=!e.target.checked;d.Settings.Settings.instance().moduleSetting("show-css-property-documentation-on-hover").set(t)}#s(){const e=this.#C.description,t=this.#C.references?.[0].url;me(ye`
      <div class="docs-popup-wrapper">
        ${e?ye`
          <div id="description">
            ${e}
          </div>
        `:n.nothing}
        ${t?ye`
          <div class="docs-popup-section footer">
            <x-link
              id="learn-more"
              href=${t}
              class="clickable underlined unbreakable-text"
            >
              ${ge(ue.learnMore)}
            </x-link>
            <label class="dont-show">
              <input type="checkbox" @change=${this.#E} jslog=${s.toggle("css-property-doc").track({change:!0})}/>
              ${ge(ue.dontShow)}
            </label>
          </div>
        `:n.nothing}
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-css-property-docs-view",ve);var fe=Object.freeze({__proto__:null,CSSPropertyDocsView:ve});const be=new Set(["tb","tb-rl","vertical-lr","vertical-rl"]);function xe(e){if("left-to-right"===e)return"right-to-left";if("right-to-left"===e)return"left-to-right";if("top-to-bottom"===e)return"bottom-to-top";if("bottom-to-top"===e)return"top-to-bottom";throw new Error("Unknown PhysicalFlexDirection")}function we(e){return{...e,"row-reverse":xe(e.row),"column-reverse":xe(e.column)}}function Se(e){const t="rtl"===e.get("direction"),o=e.get("writing-mode");return we(o&&be.has(o)?{row:t?"bottom-to-top":"top-to-bottom",column:"vertical-lr"===o?"left-to-right":"right-to-left"}:{row:t?"right-to-left":"left-to-right",column:"top-to-bottom"})}function ke(e){let t=!0,o=!1,n=-90;return"right-to-left"===e?(n=90,o=!1,t=!1):"top-to-bottom"===e?(n=0,t=!1,o=!1):"bottom-to-top"===e&&(n=0,t=!1,o=!0),{iconName:"flex-direction",rotate:n,scaleX:t?-1:1,scaleY:o?-1:1}}function $e(e,t){return{iconName:e,rotate:"right-to-left"===t?90:"left-to-right"===t?-90:0,scaleX:1,scaleY:1}}function Ne(e,t){return{iconName:e,rotate:"top-to-bottom"===t?90:"bottom-to-top"===t?-90:0,scaleX:"right-to-left"===t?-1:1,scaleY:1}}function Ce(e,t){return{iconName:e,rotate:"top-to-bottom"===t?90:"bottom-to-top"===t?-90:0,scaleX:"right-to-left"===t?-1:1,scaleY:1}}function Ee(e,t){return{iconName:e,rotate:"right-to-left"===t?90:"left-to-right"===t?-90:0,scaleX:1,scaleY:1}}function Me(e){return function(t){return ke(Se(t)[e])}}function Te(e){return function(t){const o=Se(t),n=new Map([["column",o.row],["row",o.column],["column-reverse",o.row],["row-reverse",o.column]]),r=t.get("flex-direction")||"row",i=n.get(r);if(!i)throw new Error("Unknown direction for flex-align icon");return $e(e,i)}}function Le(e){return function(t){const o=Se(t);return $e(e,o.column)}}function ze(e){return function(t){const o=Se(t);return Ne(e,o[t.get("flex-direction")||"row"])}}function Oe(e){return function(t){const o=Se(t);return Ne(e,o.row)}}function De(e){return function(t){const o=Se(t);return Ce(e,o.row)}}function je(e){return function(t){const o=Se(t),n=new Map([["column",o.row],["row",o.column],["column-reverse",o.row],["row-reverse",o.column]]),r=t.get("flex-direction")||"row",i=n.get(r);if(!i)throw new Error("Unknown direction for flex-align icon");return Ee(e,i)}}function Ie(e){return function(t){const o=Se(t);return Ee(e,o.column)}}function _e(){return{iconName:"align-items-baseline",rotate:0,scaleX:1,scaleY:1}}function Pe(e){return function(t,o){return je(e)(o)}}function Ae(e){return function(t,o){return Ie(e)(o)}}function Be(e,t){return{iconName:e,rotate:"bottom-to-top"===t||"top-to-bottom"===t?90:0,scaleX:1,scaleY:1}}function He(e){return function(t){const o=Se(t),n=t.get("flex-direction")||"row";return Be(e,o[n])}}const Re=new Map([["flex-direction: row",Me("row")],["flex-direction: column",Me("column")],["flex-direction: column-reverse",Me("column-reverse")],["flex-direction: row-reverse",Me("row-reverse")],["flex-direction: initial",Me("row")],["flex-direction: unset",Me("row")],["flex-direction: revert",Me("row")],["align-content: center",Te("align-content-center")],["align-content: space-around",Te("align-content-space-around")],["align-content: space-between",Te("align-content-space-between")],["align-content: stretch",Te("align-content-stretch")],["align-content: space-evenly",Te("align-content-space-evenly")],["align-content: flex-end",Te("align-content-end")],["align-content: flex-start",Te("align-content-start")],["align-content: start",Te("align-content-start")],["align-content: end",Te("align-content-end")],["align-content: normal",Te("align-content-stretch")],["align-content: revert",Te("align-content-stretch")],["align-content: unset",Te("align-content-stretch")],["align-content: initial",Te("align-content-stretch")],["justify-content: center",ze("justify-content-center")],["justify-content: space-around",ze("justify-content-space-around")],["justify-content: space-between",ze("justify-content-space-between")],["justify-content: space-evenly",ze("justify-content-space-evenly")],["justify-content: flex-end",ze("justify-content-end")],["justify-content: flex-start",ze("justify-content-start")],["align-items: stretch",je("align-items-stretch")],["align-items: flex-end",je("align-items-end")],["align-items: flex-start",je("align-items-start")],["align-items: center",je("align-items-center")],["align-items: baseline",_e],["align-content: baseline",_e],["flex-wrap: wrap",He("flex-wrap")],["flex-wrap: nowrap",He("flex-no-wrap")]]),qe=new Map([["align-self: baseline",_e],["align-self: center",Pe("align-self-center")],["align-self: flex-start",Pe("align-self-start")],["align-self: flex-end",Pe("align-self-end")],["align-self: stretch",Pe("align-self-stretch")]]),Ve=new Map([["align-content: center",Le("align-content-center")],["align-content: space-around",Le("align-content-space-around")],["align-content: space-between",Le("align-content-space-between")],["align-content: stretch",Le("align-content-stretch")],["align-content: space-evenly",Le("align-content-space-evenly")],["align-content: end",Le("align-content-end")],["align-content: start",Le("align-content-start")],["align-content: baseline",_e],["justify-content: center",Oe("justify-content-center")],["justify-content: space-around",Oe("justify-content-space-around")],["justify-content: space-between",Oe("justify-content-space-between")],["justify-content: space-evenly",Oe("justify-content-space-evenly")],["justify-content: end",Oe("justify-content-end")],["justify-content: start",Oe("justify-content-start")],["align-items: stretch",Ie("align-items-stretch")],["align-items: end",Ie("align-items-end")],["align-items: start",Ie("align-items-start")],["align-items: center",Ie("align-items-center")],["align-items: baseline",_e],["justify-items: center",De("justify-items-center")],["justify-items: stretch",De("justify-items-stretch")],["justify-items: end",De("justify-items-end")],["justify-items: start",De("justify-items-start")],["justify-items: baseline",_e]]),Fe=new Map([["align-self: baseline",_e],["align-self: center",Ae("align-self-center")],["align-self: start",Ae("align-self-start")],["align-self: end",Ae("align-self-end")],["align-self: stretch",Ae("align-self-stretch")]]),Ue=e=>{const t=e?.get("display");return"flex"===t||"inline-flex"===t},Ge=e=>{const t=e?.get("display");return"grid"===t||"inline-grid"===t};function We(e,t){const o=Re.get(e);return o?o(t||new Map):null}function Qe(e,t,o){const n=qe.get(e);return n?n(t||new Map,o||new Map):null}function Ye(e,t){const o=Ve.get(e);return o?o(t||new Map):null}function Xe(e,t,o){const n=Fe.get(e);return n?n(t||new Map,o||new Map):null}var Ke=Object.freeze({__proto__:null,reverseDirection:xe,getPhysicalDirections:Se,rotateFlexDirectionIcon:ke,rotateAlignContentIcon:$e,rotateJustifyContentIcon:Ne,rotateJustifyItemsIcon:Ce,rotateAlignItemsIcon:Ee,roateFlexWrapIcon:Be,findIcon:function(e,t,o){if(Ue(t)){const o=We(e,t);if(o)return o}if(Ue(o)){const n=Qe(e,t,o);if(n)return n}if(Ge(t)){const o=Ye(e,t);if(o)return o}if(Ge(o)){const n=Xe(e,t,o);if(n)return n}return null},findFlexContainerIcon:We,findFlexItemIcon:Qe,findGridContainerIcon:Ye,findGridItemIcon:Xe});const Je=new CSSStyleSheet;Je.replaceSync('*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;--legacy-accent-color:#1a73e8;--legacy-accent-fg-color:#1a73e8;--legacy-accent-color-hover:#3b86e8;--legacy-accent-fg-color-hover:#1567d3;--legacy-active-control-bg-color:#5a5a5a;--legacy-focus-bg-color:hsl(214deg 40% 92%);--legacy-focus-ring-inactive-shadow-color:#e0e0e0;--legacy-input-validation-error:#db1600;--legacy-toolbar-hover-bg-color:#eaeaea;--legacy-selection-fg-color:#fff;--legacy-selection-bg-color:var(--legacy-accent-color);--legacy-selection-inactive-fg-color:#5a5a5a;--legacy-selection-inactive-bg-color:#dadada;--legacy-divider-border:1px solid var(--sys-color-divider);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-focus-ring-active-shadow:0 0 0 1px var(--legacy-accent-color);--legacy-item-selection-bg-color:#cfe8fc;--legacy-item-selection-inactive-bg-color:#e0e0e0;--monospace-font-size:10px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace;--sys-motion-duration-short4:200ms;--sys-motion-duration-medium2:300ms;--sys-motion-duration-long2:500ms;--sys-motion-easing-emphasized:cubic-bezier(0.2,0,0,1);--sys-motion-easing-emphasized-decelerate:cubic-bezier(0.05,0.7,0.1,1);--sys-motion-easing-emphasized-accelerate:cubic-bezier(0.2,0,0,1)}.theme-with-dark-background{color-scheme:dark;--legacy-accent-color:#0e639c;--legacy-accent-fg-color:#ccc;--legacy-accent-fg-color-hover:#fff;--legacy-accent-color-hover:rgb(17 119 187);--legacy-active-control-bg-color:#cdcdcd;--legacy-focus-bg-color:hsl(214deg 19% 27%);--legacy-focus-ring-inactive-shadow-color:#5a5a5a;--legacy-toolbar-hover-bg-color:#202020;--legacy-selection-fg-color:#cdcdcd;--legacy-selection-inactive-fg-color:#cdcdcd;--legacy-selection-inactive-bg-color:hsl(0deg 0% 28%);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-item-selection-bg-color:hsl(207deg 88% 22%);--legacy-item-selection-inactive-bg-color:#454545}body{--default-font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:var(--default-font-family);font-size:12px;tab-size:4;user-select:none;color:var(--sys-color-on-surface);background:var(--sys-color-cdt-base-container)}.platform-linux{--default-font-family:"Google Sans Text","Google Sans",system-ui,sans-serif}.platform-mac{--default-font-family:system-ui,sans-serif}.platform-windows{--default-font-family:system-ui,sans-serif}:focus{outline-width:0}.platform-mac,\n:host-context(.platform-mac){--monospace-font-size:11px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace}.platform-windows,\n:host-context(.platform-windows){--monospace-font-size:12px;--monospace-font-family:monospace;--source-code-font-size:12px;--source-code-font-family:monospace}.platform-linux,\n:host-context(.platform-linux){--monospace-font-size:11px;--monospace-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace;--source-code-font-size:11px;--source-code-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)!important}.source-code{font-family:var(--source-code-font-family);font-size:var(--source-code-font-size)!important;white-space:pre-wrap}.source-code .devtools-link.text-button{max-width:100%;overflow:hidden;text-overflow:ellipsis}img{-webkit-user-drag:none}iframe,\na img{border:none}.fill{position:absolute;top:0;left:0;right:0;bottom:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > .toolbar{border-bottom:1px solid var(--sys-color-divider)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--sys-color-cdt-base-container)}iframe.widget{position:absolute;width:100%;height:100%;left:0;right:0;top:0;bottom:0}.hidden{display:none!important}.highlighted-search-result{border-radius:1px;background-color:var(--sys-color-yellow-container);outline:1px solid var(--sys-color-yellow-container)}.link{cursor:pointer;text-decoration:underline;color:var(--sys-color-primary);outline-offset:2px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}select option,\nselect optgroup,\ninput{background-color:var(--sys-color-cdt-base-container)}input{color:inherit;&[type="checkbox"]{position:relative;&:hover::after,\n    &:active::before{content:"";height:24px;width:24px;border-radius:var(--sys-shape-corner-full);position:absolute;top:-6px;left:-6px}&:not(.-theme-preserve){accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}&:not(:disabled):hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:not(:disabled):active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:not(:disabled):focus-visible{outline:none;&::before{content:"";height:15px;width:15px;border-radius:5px;position:absolute;top:-3.5px;left:-3.5px;border:2px solid var(--sys-color-state-focus-ring)}}&.small:hover::after,\n    &.small:active::before{height:12px;width:12px;top:0;left:0;border-radius:2px}}}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.theme-with-dark-background input::placeholder,\n:host-context(.theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}.harmony-input:not([type]),\n.harmony-input[type="number"],\n.harmony-input[type="text"]{padding:3px 6px;height:24px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;&.error-input,\n  &:invalid{border-color:var(--sys-color-error)}&:not(.error-input):not(:invalid):focus{border-color:var(--sys-color-state-focus-ring)}&:not(.error-input):not(:invalid):hover:not(:focus){background:var(--sys-color-state-hover-on-subtle)}}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(--sys-color-on-surface)!important;text-decoration:none!important}.chrome-select{appearance:none;user-select:none;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;color:var(--sys-color-on-surface);font:inherit;margin:0;outline:none;padding-right:20px;padding-left:6px;background-image:var(--image-file-arrow-drop-down-light);background-color:var(--sys-color-surface);background-position:right center;background-repeat:no-repeat;min-height:24px;min-width:80px}.chrome-select:disabled{opacity:38%}.theme-with-dark-background .chrome-select,\n:host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.chrome-select:enabled{&:hover{background-color:var(--sys-color-state-hover-on-subtle)}&:active{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}}@media (forced-colors: active) and (prefers-color-scheme: light){.chrome-select{background-image:var(--image-file-arrow-drop-down-light)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){.chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}}.chrome-select-label{margin:0 22px;flex:none}.chrome-select-label p p{margin-top:0;color:var(--sys-color-token-subtle)}.settings-select{margin:0}.chrome-select optgroup,\n.chrome-select option{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--sys-color-token-subtle);white-space:nowrap}span[is="dt-icon-label"]{flex:none}.full-widget-dimmed-banner a{color:inherit}.full-widget-dimmed-banner{color:var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;text-align:center;padding:20px;position:absolute;top:0;right:0;bottom:0;left:0;font-size:13px;overflow:auto;z-index:500}.dot::before{content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--sys-color-green-bright)}.purple::before{background-color:var(--sys-color-purple-bright)}.expandable-inline-button{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);cursor:pointer;border-radius:3px}.undisplayable-text,\n.expandable-inline-button{border:none;padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,\n.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--sys-color-state-disabled);font-style:italic}.expandable-inline-button:hover,\n.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-focus-highlight)}::selection{background-color:var(--sys-color-tonal-container)}.reload-warning{align-self:center;margin-left:10px}button.link{border:none;background:none;padding:3px}button.link:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:var(--sys-shape-corner-full)}.theme-with-dark-background button.link:focus-visible,\n:host-context(.theme-with-dark-background) button.link:focus-visible{--override-link-focus-background-color:rgb(230 230 230/8%)}@media (forced-colors: active){.dimmed,\n  .chrome-select:disabled{opacity:100%}.harmony-input:not([type]),\n  .harmony-input[type="number"],\n  .harmony-input[type="text"]{border:1px solid ButtonText}.harmony-input:not([type]):focus,\n  .harmony-input[type="number"]:focus,\n  .harmony-input[type="text"]:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;width:16px;height:15px;margin-right:0;opacity:70%;mask-image:var(--image-file-cross-circle-filled);mask-position:center;mask-repeat:no-repeat;mask-size:99%;background-color:var(--icon-default)}input.custom-search-input::-webkit-search-cancel-button:hover{opacity:99%}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--sys-color-token-subtle));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:"";position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-flex;vertical-align:middle}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.theme-with-dark-background) devtools-adorner{--override-adorner-border-color:var(--sys-color-tonal-outline);--override-adorner-focus-border-color:var(--sys-color-state-focus-ring);--override-adorner-active-background-color:var(--sys-color-state-riple-neutral-on-subtle)}.panel{display:flex;overflow:hidden;position:absolute;top:0;left:0;right:0;bottom:0;z-index:0;background-color:var(--sys-color-cdt-base-container)}.panel-sidebar{overflow-x:hidden;background-color:var(--sys-color-cdt-base-container)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}\n/*# sourceURL=inspectorCommon.css */\n');const Ze=new CSSStyleSheet;Ze.replaceSync(".query:not(.editing-query){overflow:hidden}.editable .query-text{color:var(--sys-color-on-surface)}.editable .query-text:hover{text-decoration:var(--override-styles-section-text-hover-text-decoration);cursor:var(--override-styles-section-text-hover-cursor)}\n/*# sourceURL=cssQuery.css */\n");const{render:et,html:tt}=n;class ot extends HTMLElement{static litTagName=n.literal`devtools-css-query`;#e=this.attachShadow({mode:"open"});#M="";#T;#L="";#z;#O;set data(e){this.#M=e.queryPrefix,this.#T=e.queryName,this.#L=e.queryText,this.#z=e.onQueryTextClick,this.#O=e.jslogContext,this.#s()}connectedCallback(){this.#e.adoptedStyleSheets=[Ze,Je]}#s(){const e=n.Directives.classMap({query:!0,editable:Boolean(this.#z)}),t=tt`
      <span class="query-text" @click=${this.#z}>${this.#L}</span>
    `;et(tt`
      <div class=${e} jslog=${s.cssRuleHeader(this.#O).track({click:!0,change:!0})}>
        <slot name="indent"></slot>${this.#M?tt`<span>${this.#M+" "}</span>`:n.nothing}${this.#T?tt`<span>${this.#T+" "}</span>`:n.nothing}${t} {
      </div>
    `,this.#e,{host:this})}}customElements.define("devtools-css-query",ot);var nt=Object.freeze({__proto__:null,CSSQuery:ot});const rt=new CSSStyleSheet;rt.replaceSync(".registered-property-popup-wrapper{max-width:232px;font-size:12px;line-height:1.4}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)}:host{padding:11px 7px}.divider{margin:8px -7px;border:1px solid var(--sys-color-divider)}.registered-property-links{margin-top:8px}.clickable{color:var(--sys-color-primary);cursor:pointer}.underlined{text-decoration:underline}.unbreakable-text{white-space:nowrap}.css-property{color:var(--webkit-css-property-color,var(--sys-color-token-property-special))}.title{color:var(--sys-color-state-disabled)}\n/*# sourceURL=cssVariableValueView.css */\n");const it={registeredPropertyLinkTitle:"View registered property",invalidPropertyValue:"Invalid property value, expected type {type}",sIsNotDefined:"{PH1} is not defined"},st=e.i18n.registerUIStrings("panels/elements/components/CSSVariableValueView.ts",it),at=e.i18n.getLocalizedString.bind(void 0,st),lt=n.i18nTemplate.bind(void 0,st),{render:ct,html:dt}=n;function pt(e){return dt`<div class="registered-property-links">
            <span role="button" @click=${e?.goToDefinition} class="clickable underlined unbreakable-text"}>
              ${at(it.registeredPropertyLinkTitle)}
            </span>
          </div>`}class ut extends HTMLElement{static litTagName=n.literal`devtools-css-variable-parser-error`;#e=this.attachShadow({mode:"open"});constructor(e){super(),this.#e.adoptedStyleSheets=[rt],this.#s(e)}#s(e){const t=dt`<span class="monospace css-property">${e.registration.syntax()}</span>`;ct(dt`
      <div class="variable-value-popup-wrapper">
        ${lt(it.invalidPropertyValue,{type:t})}
        ${pt(e)}
      </div>`,this.#e,{host:this})}}class ht extends HTMLElement{static litTagName=n.literal`devtools-css-variable-value-view`;#e=this.attachShadow({mode:"open"});variableName;value;details;constructor({variableName:e,value:t,details:o}){super(),this.#e.adoptedStyleSheets=[rt],this.variableName=e,this.value=t,this.details=o,this.#s()}#s(){const e=this.details?.registration.initialValue(),t=this.details?dt`
        <hr class=divider />
        <div class=registered-property-popup-wrapper>
          <div class="monospace">
            <div><span class="css-property">syntax:</span> ${this.details.registration.syntax()}</div>
            <div><span class="css-property">inherits:</span> ${this.details.registration.inherits()}</div>
            ${e?dt`<div><span class="css-property">initial-value:</span> ${e}</div>`:""}
          </div>
          ${pt(this.details)}
        </div>`:"",o=this.value??at(it.sIsNotDefined,{PH1:this.variableName});ct(dt`<div class="variable-value-popup-wrapper">
               ${o}
             </div>
             ${t}
             `,this.#e,{host:this})}}customElements.define("devtools-css-variable-value-view",ht),customElements.define("devtools-css-variable-parser-error",ut);var gt=Object.freeze({__proto__:null,CSSVariableParserError:ut,CSSVariableValueView:ht});const mt=new CSSStyleSheet;mt.replaceSync(":host{--override-node-text-label-color:var(--sys-color-token-tag);--override-node-text-class-color:var(--sys-color-token-attribute);--override-node-text-id-color:var(--sys-color-token-attribute);--override-node-text-multiple-descriptors-id:var(--sys-color-on-surface);--override-node-text-multiple-descriptors-class:var(--sys-color-token-property)}.crumbs{display:inline-flex;align-items:stretch;width:100%;overflow:hidden;pointer-events:auto;cursor:default;white-space:nowrap;position:relative;background:var(--sys-color-cdt-base-container);font-size:inherit;font-family:inherit}.crumbs-window{flex-grow:2;overflow:hidden}.crumbs-scroll-container{display:inline-flex;margin:0;padding:0}.crumb{display:block;padding:0 7px;line-height:23px;white-space:nowrap}.overflow{padding:0 5px;font-weight:bold;display:block;border:none;flex-grow:0;flex-shrink:0;text-align:center;background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-token-subtle);margin:1px;outline:1px solid var(--sys-color-neutral-outline)}.overflow.hidden{display:none}.overflow:disabled{opacity:50%}.overflow:focus{outline-color:var(--sys-color-primary)}.overflow:not(:disabled):hover{background-color:var(--sys-color-state-hover-on-subtle);color:var(--sys-color-on-surface)}.crumb-link{text-decoration:none;color:inherit}.crumb:hover{background:var(--sys-color-state-hover-on-subtle)}.crumb.selected{background:var(--sys-color-tonal-container)}.crumb:focus{outline:var(--sys-color-primary) auto 1px}\n/*# sourceURL=elementsBreadcrumbs.css */\n");const yt={text:"(text)"},vt=e.i18n.registerUIStrings("panels/elements/components/ElementsBreadcrumbsUtils.ts",yt),ft=e.i18n.getLocalizedString.bind(void 0,vt),bt=(e,t)=>t?e.filter((e=>e.nodeType!==Node.DOCUMENT_NODE)).map((e=>({title:wt(e),selected:e.id===t.id,node:e,originalNode:e.legacyDomNode}))).reverse():[],xt=(e,t={})=>({main:e,extras:t}),wt=e=>{switch(e.nodeType){case Node.ELEMENT_NODE:{if(e.pseudoType)return xt("::"+e.pseudoType);const t=xt(e.nodeNameNicelyCased),o=e.getAttribute("id");o&&(t.extras.id=o);const n=e.getAttribute("class");if(n){const e=new Set(n.split(/\s+/));t.extras.classes=Array.from(e)}return t}case Node.TEXT_NODE:return xt(ft(yt.text));case Node.COMMENT_NODE:return xt("\x3c!--\x3e");case Node.DOCUMENT_TYPE_NODE:return xt("<!doctype>");case Node.DOCUMENT_FRAGMENT_NODE:return xt(e.shadowRootType?"#shadow-root":e.nodeNameNicelyCased);default:return xt(e.nodeNameNicelyCased)}};var St=Object.freeze({__proto__:null,crumbsToRender:bt,determineElementTitle:wt});const kt={breadcrumbs:"DOM tree breadcrumbs",scrollLeft:"Scroll left",scrollRight:"Scroll right"},$t=e.i18n.registerUIStrings("panels/elements/components/ElementsBreadcrumbs.ts",kt),Nt=e.i18n.getLocalizedString.bind(void 0,$t);class Ct extends Event{static eventName="breadcrumbsnodeselected";legacyDomNode;constructor(e){super(Ct.eventName,{}),this.legacyDomNode=e.legacyDomNode}}const Et=o.RenderCoordinator.RenderCoordinator.instance();class Mt extends HTMLElement{static litTagName=n.literal`devtools-elements-breadcrumbs`;#e=this.attachShadow({mode:"open"});#D=new ResizeObserver((()=>this.#j()));#I=this.#s.bind(this);#_=[];#P=null;#A=!1;#B="start";#H=!1;#R=!1;connectedCallback(){this.#e.adoptedStyleSheets=[mt]}set data(e){this.#P=e.selectedNode,this.#_=e.crumbs,this.#R=!1,p.ScheduledRender.scheduleRender(this,this.#I)}disconnectedCallback(){this.#H=!1,this.#D.disconnect()}#q(e){return t=>{t.preventDefault(),this.dispatchEvent(new Ct(e))}}async#j(){const e=this.#e.querySelector(".crumbs-scroll-container"),t=this.#e.querySelector(".crumbs-window");if(!e||!t)return;const o=await Et.read((()=>t.clientWidth)),n=await Et.read((()=>e.clientWidth));this.#A?n<o&&(this.#A=!1):n>o&&(this.#A=!0),this.#V(),this.#F(t)}#U(e){return()=>e.highlightNode()}#G(e){return()=>e.clearHighlight()}#W(e){return()=>e.highlightNode()}#Q(e){return()=>e.clearHighlight()}#Y(){if(!this.#D||!0===this.#H)return;const e=this.#e.querySelector(".crumbs");e&&(this.#D.observe(e),this.#H=!0)}async#X(){const e=this.#e.querySelector(".crumbs-scroll-container"),t=this.#e.querySelector(".crumbs-window");if(!e||!t)return;const o=await Et.read((()=>t.clientWidth)),n=await Et.read((()=>e.clientWidth));this.#A?n<o&&(this.#A=!1,this.#s()):n>o&&(this.#A=!0,this.#s())}#K(e){if(!e.target)return;const t=e.target;this.#F(t)}#F(e){const t=e.scrollWidth-e.clientWidth,o=e.scrollLeft;this.#B=o<10?"start":o>=t-10?"end":"middle",this.#s()}#J(e){return()=>{this.#R=!0;const t=this.#e.querySelector(".crumbs-window");if(!t)return;const o=t.clientWidth/2,n="left"===e?Math.max(Math.floor(t.scrollLeft-o),0):t.scrollLeft+o;t.scrollTo({behavior:"smooth",left:n})}}#Z(e,t){const o=n.Directives.classMap({overflow:!0,[e]:!0,hidden:!this.#A}),r=Nt("left"===e?kt.scrollLeft:kt.scrollRight);return n.html`
      <button
        class=${o}
        @click=${this.#J(e)}
        ?disabled=${t}
        aria-label=${r}
        title=${r}>
        <${a.Icon.Icon.litTagName} .data=${{iconName:"triangle-"+e,color:"var(--sys-color-on-surface)",width:"12px",height:"10px"}}>
        </${a.Icon.Icon.litTagName}>
      </button>
      `}#s(){const e=bt(this.#_,this.#P);n.render(n.html`
      <nav class="crumbs" aria-label=${Nt(kt.breadcrumbs)} jslog=${s.elementsBreadcrumbs()}>
        ${this.#Z("left","start"===this.#B)}

        <div class="crumbs-window" @scroll=${this.#K}>
          <ul class="crumbs-scroll-container">
            ${e.map((e=>{const t={crumb:!0,selected:e.selected};return n.html`
                <li class=${n.Directives.classMap(t)}
                  data-node-id=${e.node.id}
                  data-crumb="true"
                >
                  <a href="#"
                    draggable=false
                    class="crumb-link"
                    jslog=${s.item().track({click:!0})}
                    @click=${this.#q(e.node)}
                    @mousemove=${this.#U(e.node)}
                    @mouseleave=${this.#G(e.node)}
                    @focus=${this.#W(e.node)}
                    @blur=${this.#Q(e.node)}
                  ><${u.NodeText.NodeText.litTagName} data-node-title=${e.title.main} .data=${{nodeTitle:e.title.main,nodeId:e.title.extras.id,nodeClasses:e.title.extras.classes}}></${u.NodeText.NodeText.litTagName}></a>
                </li>`}))}
          </ul>
        </div>
        ${this.#Z("right","end"===this.#B)}
      </nav>
    `,this.#e,{host:this}),this.#X(),this.#Y(),this.#V()}async#V(){if(!this.#P||!this.#e||!this.#A||this.#R)return;const e=this.#P.id,t=this.#e.querySelector(`.crumb[data-node-id="${e}"]`);t&&await Et.scroll((()=>{t.scrollIntoView({behavior:"auto"})}))}}customElements.define("devtools-elements-breadcrumbs",Mt);var Tt=Object.freeze({__proto__:null,NodeSelectedEvent:Ct,ElementsBreadcrumbs:Mt});const Lt=new CSSStyleSheet;Lt.replaceSync(':host{display:inline-flex;vertical-align:middle}:host(.hidden){display:none}.expand-button{display:inline-flex;justify-content:center;align-items:center;box-sizing:border-box;width:14px;height:10px;margin:0 2px;border:1px solid var(--override-adorner-border-color,var(--sys-color-tonal-outline));border-radius:10px;background:var(--override-adorner-background-color,var(--sys-color-cdt-base-container));padding:0;position:relative;&:hover::after,\n  &:active::before{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}&:hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}}.expand-button devtools-icon{width:14px;height:14px;color:var(--sys-color-primary)}\n/*# sourceURL=elementsTreeExpandButton.css */\n');const zt={expand:"Expand"},Ot=e.i18n.registerUIStrings("panels/elements/components/ElementsTreeExpandButton.ts",zt),Dt=e.i18n.getLocalizedString.bind(void 0,Ot);class jt extends HTMLElement{static litTagName=n.literal`devtools-elements-tree-expand-button`;#e=this.attachShadow({mode:"open"});#ee=()=>{};set data(e){this.#ee=e.clickHandler,this.#te()}#te(){this.#s()}connectedCallback(){this.#e.adoptedStyleSheets=[Lt]}#s(){n.render(n.html`<button
        class="expand-button"
        tabindex="-1"
        aria-label=${Dt(zt.expand)}
        jslog=${s.action("expand").track({click:!0})}
        @click=${this.#ee}><${a.Icon.Icon.litTagName} name="dots-horizontal"></${a.Icon.Icon.litTagName}></button>`,this.#e,{host:this})}}customElements.define("devtools-elements-tree-expand-button",jt);var It=Object.freeze({__proto__:null,ElementsTreeExpandButton:jt});const _t=e=>({parentNode:e.parentNode?_t(e.parentNode):null,id:e.id,nodeType:e.nodeType(),pseudoType:e.pseudoType(),shadowRootType:e.shadowRootType(),nodeName:e.nodeName(),nodeNameNicelyCased:e.nodeNameInCorrectCase(),legacyDomNode:e,highlightNode:t=>e.highlight(t),clearHighlight:()=>h.OverlayModel.OverlayModel.hideDOMNodeHighlight(),getAttribute:e.getAttribute.bind(e)});var Pt=Object.freeze({__proto__:null,legacyNodeToElementsComponentsNode:_t});const At=new CSSStyleSheet;At.replaceSync('*{box-sizing:border-box;font-size:12px}.header{background-color:var(--sys-color-surface2);border-bottom:1px solid var(--sys-color-divider);line-height:1.6;overflow:hidden;padding:0 5px;white-space:nowrap}.header::marker{color:var(--sys-color-on-surface-subtle);font-size:11px;line-height:1}.header:focus{background-color:var(--sys-color-tonal-container)}.content-section{padding:16px;border-bottom:1px solid var(--sys-color-divider);overflow-x:hidden}.content-section-title{font-size:12px;font-weight:500;line-height:1.1;margin:0;padding:0}.checkbox-settings{margin-top:8px;display:grid;grid-template-columns:1fr;gap:5px}.checkbox-label{display:flex;flex-direction:row;align-items:center;min-width:40px;width:fit-content}.checkbox-settings .checkbox-label{margin-bottom:8px}.checkbox-settings .checkbox-label:last-child{margin-bottom:0}.checkbox-label input{margin:0 6px 0 0;padding:0;flex:none}.checkbox-label input:focus{outline:auto 5px -webkit-focus-ring-color}.checkbox-label > span{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.select-settings{margin-top:16px;width:fit-content}.select-label{display:flex;flex-direction:column}.select-label span{margin-bottom:4px}.elements{margin-top:12px;color:var(--sys-color-token-tag);display:grid;grid-template-columns:repeat(auto-fill,minmax(min(250px,100%),1fr));gap:8px}.element{display:flex;flex-direction:row;align-items:center;gap:8px}.show-element{flex:none}.chrome-select{min-width:0;max-width:150px}.color-picker{opacity:0%}.color-picker-label{border:1px solid var(--sys-color-neutral-outline);cursor:default;display:inline-block;flex:none;height:10px;width:10px;position:relative;&:focus-within{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:2px}}.color-picker-label input[type="color"]{width:100%;height:100%;position:absolute}.color-picker-label:hover,\n.color-picker-label:focus{border:1px solid var(--sys-color-outline);transform:scale(1.2)}.node-text-container{line-height:16px;padding:0 0.5ex;border-radius:5px}\n/*# sourceURL=layoutPane.css */\n');const Bt={chooseElementOverlayColor:"Choose the overlay color for this element",showElementInTheElementsPanel:"Show element in the Elements panel",grid:"Grid",overlayDisplaySettings:"Overlay display settings",gridOverlays:"Grid overlays",noGridLayoutsFoundOnThisPage:"No grid layouts found on this page",flexbox:"Flexbox",flexboxOverlays:"Flexbox overlays",noFlexboxLayoutsFoundOnThisPage:"No flexbox layouts found on this page",colorPickerOpened:"Color picker opened."},Ht=e.i18n.registerUIStrings("panels/elements/components/LayoutPane.ts",Bt),Rt=e.i18n.getLocalizedString.bind(void 0,Ht),{render:qt,html:Vt}=n,Ft=e=>{const t=e.getAttribute("class");return{id:e.id,color:"var(--sys-color-inverse-surface)",name:e.localName(),domId:e.getAttribute("id"),domClasses:t?t.split(/\s+/).filter((e=>Boolean(e))):void 0,enabled:!1,reveal:()=>{d.Revealer.reveal(e),e.scrollIntoView()},highlight:()=>{e.highlight()},hideHighlight:()=>{h.OverlayModel.OverlayModel.hideDOMNodeHighlight()},toggle:e=>{throw new Error("Not implemented")},setColor(e){throw new Error("Not implemented")}}},Ut=e=>e.map((e=>{const t=Ft(e),o=e.id;return{...t,color:e.domModel().overlayModel().colorOfGridInPersistentOverlay(o)||"var(--sys-color-inverse-surface)",enabled:e.domModel().overlayModel().isHighlightedGridInPersistentOverlay(o),toggle:t=>{t?e.domModel().overlayModel().highlightGridInPersistentOverlay(o):e.domModel().overlayModel().hideGridInPersistentOverlay(o)},setColor(t){this.color=t,e.domModel().overlayModel().setColorOfGridInPersistentOverlay(o,t)}}})),Gt=e=>e.map((e=>{const t=Ft(e),o=e.id;return{...t,color:e.domModel().overlayModel().colorOfFlexInPersistentOverlay(o)||"var(--sys-color-inverse-surface)",enabled:e.domModel().overlayModel().isHighlightedFlexContainerInPersistentOverlay(o),toggle:t=>{t?e.domModel().overlayModel().highlightFlexContainerInPersistentOverlay(o):e.domModel().overlayModel().hideFlexContainerInPersistentOverlay(o)},setColor(t){this.color=t,e.domModel().overlayModel().setColorOfFlexInPersistentOverlay(o,t)}}}));function Wt(e){return"enum"===e.type}function Qt(e){return"boolean"===e.type}const Yt=o.RenderCoordinator.RenderCoordinator.instance();let Xt;class Kt extends g.LegacyWrapper.WrappableComponent{static litTagName=n.literal`devtools-layout-pane`;#e=this.attachShadow({mode:"open"});#u=[];#oe;#ne;constructor(){super(),this.#u=this.#re(),this.#oe=d.Settings.Settings.instance().moduleSetting("show-ua-shadow-dom"),this.#ne=[],this.#e.adoptedStyleSheets=[i.checkboxStyles,At,Je]}static instance(){return Xt||(Xt=g.LegacyWrapper.legacyWrapper(c.Widget.Widget,new Kt)),Xt.element.style.minWidth="min-content",Xt.element.setAttribute("jslog",`${s.pane("layout").track({resize:!0})}`),Xt.getComponent()}modelAdded(e){const t=e.overlayModel();t.addEventListener("PersistentGridOverlayStateChanged",this.render,this),t.addEventListener("PersistentFlexContainerOverlayStateChanged",this.render,this),this.#ne.push(e)}modelRemoved(e){const t=e.overlayModel();t.removeEventListener("PersistentGridOverlayStateChanged",this.render,this),t.removeEventListener("PersistentFlexContainerOverlayStateChanged",this.render,this),this.#ne=this.#ne.filter((t=>t!==e))}async#ie(e){const t=this.#oe.get(),o=[];for(const n of this.#ne)try{const r=await n.getNodesByStyle(e,!0);for(const e of r){const r=n.nodeForId(e);null===r||!t&&r.ancestorUserAgentShadowRoot()||o.push(r)}}catch(e){console.warn(e)}return o}async#se(){return await this.#ie([{name:"display",value:"grid"},{name:"display",value:"inline-grid"}])}async#ae(){return await this.#ie([{name:"display",value:"flex"},{name:"display",value:"inline-flex"}])}#re(){const e=[];for(const t of["show-grid-line-labels","show-grid-track-sizes","show-grid-areas","extend-grid-lines"]){const o=d.Settings.Settings.instance().moduleSetting(t),n=o.get(),r=o.type();if(!r)throw new Error("A setting provided to LayoutSidebarPane does not have a setting type");if("boolean"!==r&&"enum"!==r)throw new Error("A setting provided to LayoutSidebarPane does not have a supported setting type");const i={type:r,name:o.name,title:o.title()};("boolean"==typeof n||"string"==typeof n)&&e.push({...i,value:n,options:o.options().map((e=>({...e,value:e.value})))})}return e}onSettingChanged(e,t){d.Settings.Settings.instance().moduleSetting(e).set(t)}wasShown(){for(const e of this.#u)d.Settings.Settings.instance().moduleSetting(e.name).addChangeListener(this.render,this);for(const e of this.#ne)this.modelRemoved(e);this.#ne=[],h.TargetManager.TargetManager.instance().observeModels(h.DOMModel.DOMModel,this,{scoped:!0}),c.Context.Context.instance().addFlavorChangeListener(h.DOMModel.DOMNode,this.render,this),this.#oe.addChangeListener(this.render,this),this.render()}willHide(){for(const e of this.#u)d.Settings.Settings.instance().moduleSetting(e.name).removeChangeListener(this.render,this);h.TargetManager.TargetManager.instance().unobserveModels(h.DOMModel.DOMModel,this),c.Context.Context.instance().removeFlavorChangeListener(h.DOMModel.DOMNode,this.render,this),this.#oe.removeChangeListener(this.render,this)}#le(e){if(!e.target)return;const t=e.target.parentElement;if(!t)throw new Error("<details> element is not found for a <summary> element");switch(e.key){case"ArrowLeft":t.open=!1;break;case"ArrowRight":t.open=!0}}async render(){const e=Ut(await this.#se()),t=Gt(await this.#ae());await Yt.write("LayoutPane render",(()=>{qt(Vt`
        <details open>
          <summary class="header" @keydown=${this.#le} jslog=${s.sectionHeader("grid-settings").track({click:!0})}>
            ${Rt(Bt.grid)}
          </summary>
          <div class="content-section" jslog=${s.section("grid-settings")}>
            <h3 class="content-section-title">${Rt(Bt.overlayDisplaySettings)}</h3>
            <div class="select-settings">
              ${this.#ce().map((e=>this.#de(e)))}
            </div>
            <div class="checkbox-settings">
              ${this.#pe().map((e=>this.#ue(e)))}
            </div>
          </div>
          ${e?Vt`<div class="content-section" jslog=${s.section("grid-overlays")}>
              <h3 class="content-section-title">
                ${e.length?Rt(Bt.gridOverlays):Rt(Bt.noGridLayoutsFoundOnThisPage)}
              </h3>
              ${e.length?Vt`<div class="elements">
                  ${e.map((e=>this.#he(e)))}
                </div>`:""}
            </div>`:""}
        </details>
        ${void 0!==t?Vt`
          <details open>
            <summary class="header" @keydown=${this.#le} jslog=${s.sectionHeader("flexbox-overlays").track({click:!0})}>
              ${Rt(Bt.flexbox)}
            </summary>
            ${t?Vt`<div class="content-section" jslog=${s.section("flexbox-overlays")}>
                <h3 class="content-section-title">
                  ${t.length?Rt(Bt.flexboxOverlays):Rt(Bt.noFlexboxLayoutsFoundOnThisPage)}
                </h3>
                ${t.length?Vt`<div class="elements">
                    ${t.map((e=>this.#he(e)))}
                  </div>`:""}
              </div>`:""}
          </details>
          `:""}
      `,this.#e,{host:this})}))}#ce(){return this.#u.filter(Wt)}#pe(){return this.#u.filter(Qt)}#ge(e,t){t.preventDefault(),this.onSettingChanged(e.name,t.target.checked)}#me(e,t){t.preventDefault(),this.onSettingChanged(e.name,t.target.value)}#ye(e,t){t.preventDefault(),e.toggle(t.target.checked)}#ve(e,t){t.preventDefault(),e.reveal()}#fe(e,t){t.preventDefault(),e.setColor(t.target.value),this.render()}#be(e,t){t.preventDefault(),e.highlight()}#xe(e,t){t.preventDefault(),e.hideHighlight()}#he(e){const t=this.#ye.bind(this,e),o=this.#ve.bind(this,e),n=this.#fe.bind(this,e),i=this.#be.bind(this,e),a=this.#xe.bind(this,e);return Vt`<div class="element" jslog=${s.item()}>
      <label data-element="true" class="checkbox-label">
        <input data-input="true" type="checkbox" .checked=${e.enabled} @change=${t} jslog=${s.toggle().track({click:!0})} />
        <span class="node-text-container" data-label="true" @mouseenter=${i} @mouseleave=${a}>
          <${u.NodeText.NodeText.litTagName} .data=${{nodeId:e.domId,nodeTitle:e.name,nodeClasses:e.domClasses}}></${u.NodeText.NodeText.litTagName}>
        </span>
      </label>
      <label @keyup=${e=>{if("Enter"!==e.key&&" "!==e.key)return;e.target.querySelector("input").click(),c.ARIAUtils.alert(Rt(Bt.colorPickerOpened)),e.preventDefault()}} @keydown=${e=>{" "===e.key&&e.preventDefault()}} class="color-picker-label" style="background: ${e.color};" jslog=${s.showStyleEditor("color").track({click:!0})}>
        <input @change=${n} @input=${n} title=${Rt(Bt.chooseElementOverlayColor)} tabindex="0" class="color-picker" type="color" value=${e.color} />
      </label>
      <${r.Button.Button.litTagName} class="show-element"
                                           title=${Rt(Bt.showElementInTheElementsPanel)}
                                           aria-label=${Rt(Bt.showElementInTheElementsPanel)}
                                           .iconName=${"select-element"}
                                           .jslogContext=${"elements.select-element"}
                                           .size=${"SMALL"}
                                           .variant=${"icon"}
                                           @click=${o}></${r.Button.Button.litTagName}>
    </div>`}#ue(e){const t=this.#ge.bind(this,e);return Vt`<label data-boolean-setting="true" class="checkbox-label" title=${e.title} jslog=${s.toggle().track({click:!0}).context(e.name)}>
      <input data-input="true" type="checkbox" .checked=${e.value} @change=${t} />
      <span data-label="true">${e.title}</span>
    </label>`}#de(e){const o=this.#me.bind(this,e);return Vt`<label data-enum-setting="true" class="select-label" title=${e.title}>
      <select
        class="chrome-select"
        data-input="true"
        jslog=${s.dropDown().track({change:!0}).context(e.name)}
        @change=${o}>
        ${e.options.map((o=>Vt`<option value=${o.value} .selected=${e.value===o.value} jslog=${s.item(t.StringUtilities.toKebabCase(o.value)).track({click:!0})}>${o.title}</option>`))}
      </select>
    </label>`}}customElements.define("devtools-layout-pane",Kt);var Jt=Object.freeze({__proto__:null,LayoutPane:Kt}),Zt=Object.freeze({__proto__:null});const eo=new CSSStyleSheet;eo.replaceSync(".container-link{display:inline-block;color:var(--sys-color-state-disabled)}.container-link:hover{color:var(--sys-color-primary)}.queried-size-details{color:var(--sys-color-on-surface)}.axis-icon{margin-left:0.4em;width:16px;height:12px;vertical-align:text-top}.axis-icon.hidden{display:none}.axis-icon.vertical{transform:rotate(90deg)}\n/*# sourceURL=queryContainer.css */\n");const{render:to,html:oo}=n,{PhysicalAxis:no,QueryAxis:ro}=h.CSSContainerQuery;class io extends Event{static eventName="queriedsizerequested";constructor(){super(io.eventName,{})}}class so extends HTMLElement{static litTagName=n.literal`devtools-query-container`;#e=this.attachShadow({mode:"open"});#T;#we;#Se;#ke=!1;#$e;set data(e){this.#T=e.queryName,this.#we=e.container,this.#Se=e.onContainerLinkClick,this.#s()}connectedCallback(){this.#e.adoptedStyleSheets=[eo]}updateContainerQueriedSizeDetails(e){this.#$e=e,this.#s()}async#Ne(){this.#we?.highlightNode("container-outline"),this.#ke=!0,this.dispatchEvent(new io)}#Ce(){this.#we?.clearHighlight(),this.#ke=!1,this.#s()}#s(){if(!this.#we)return;let e,t;this.#T||(e=this.#we.getAttribute("id"),t=this.#we.getAttribute("class")?.split(/\s+/).filter(Boolean));const o=this.#T||this.#we.nodeNameNicelyCased;to(oo`
      →
      <a href="#"
        draggable=false
        class="container-link"
        jslog=${s.cssRuleHeader("container-query").track({click:!0})}
        @click=${this.#Se}
        @mouseenter=${this.#Ne}
        @mouseleave=${this.#Ce}
      ><${u.NodeText.NodeText.litTagName}
          data-node-title=${o}
          .data=${{nodeTitle:o,nodeId:e,nodeClasses:t}}></${u.NodeText.NodeText.litTagName}></a>
      ${this.#ke?this.#Ee():n.nothing}
    `,this.#e,{host:this})}#Ee(){if(!this.#$e||""===this.#$e.queryAxis)return n.nothing;const e="size"===this.#$e.queryAxis,t=n.Directives.classMap({"axis-icon":!0,hidden:e,vertical:"Vertical"===this.#$e.physicalAxis});return oo`
      <span class="queried-size-details">
        (${this.#$e.queryAxis}<${a.Icon.Icon.litTagName}
          class=${t} .data=${{iconName:"width",color:"var(--icon-default)"}}></${a.Icon.Icon.litTagName}>)
        ${e&&this.#$e.width?"width:":n.nothing}
        ${this.#$e.width||n.nothing}
        ${e&&this.#$e.height?"height:":n.nothing}
        ${this.#$e.height||n.nothing}
      </span>
    `}}customElements.define("devtools-query-container",so);var ao=Object.freeze({__proto__:null,QueriedSizeRequestedEvent:io,QueryContainer:so});const lo=new CSSStyleSheet;lo.replaceSync(".container{padding:12px;min-width:170px}.row{padding:0;color:var(--sys-color-on-surface);padding-bottom:16px}.row:last-child{padding-bottom:0}.property{padding-bottom:4px;white-space:nowrap}.property-name{color:var(--sys-color-token-property-special)}.property-value{color:var(--sys-color-on-surface)}.property-value.not-authored{color:var(--sys-color-state-disabled)}.buttons{display:flex;flex-direction:row}.buttons > :first-child{border-radius:3px 0 0 3px}.buttons > :last-child{border-radius:0 3px 3px 0}.button{border:1px solid var(--sys-color-neutral-outline);background-color:var(--sys-color-cdt-base-container);width:24px;height:24px;min-width:24px;min-height:24px;padding:0;margin:0;display:flex;justify-content:center;align-items:center;cursor:pointer}.button:focus-visible{outline:auto 5px -webkit-focus-ring-color}.button devtools-icon{color:var(--icon-default)}.button:hover devtools-icon{color:var(--icon-default-hover)}.button.selected devtools-icon{color:var(--icon-toggled)}\n/*# sourceURL=stylePropertyEditor.css */\n");const co={selectButton:"Add {propertyName}: {propertyValue}",deselectButton:"Remove {propertyName}: {propertyValue}"},po=e.i18n.registerUIStrings("panels/elements/components/StylePropertyEditor.ts",co),uo=e.i18n.getLocalizedString.bind(void 0,po),{render:ho,html:go,Directives:mo}=n;class yo extends Event{static eventName="propertyselected";data;constructor(e,t){super(yo.eventName,{}),this.data={name:e,value:t}}}class vo extends Event{static eventName="propertydeselected";data;constructor(e,t){super(vo.eventName,{}),this.data={name:e,value:t}}}class fo extends HTMLElement{#e=this.attachShadow({mode:"open"});#Me=new Map;#Te=new Map;editableProperties=[];constructor(){super()}connectedCallback(){this.#e.adoptedStyleSheets=[lo]}getEditableProperties(){return this.editableProperties}set data(e){this.#Me=e.authoredProperties,this.#Te=e.computedProperties,this.#s()}#s(){ho(go`
      <div class="container">
        ${this.editableProperties.map((e=>this.#Le(e)))}
      </div>
    `,this.#e,{host:this})}#Le(e){const t=this.#Me.get(e.propertyName),o=!t,n=t||this.#Te.get(e.propertyName),r=mo.classMap({"property-value":!0,"not-authored":o});return go`<div class="row">
      <div class="property">
        <span class="property-name">${e.propertyName}</span>: <span class=${r}>${n}</span>
      </div>
      <div class="buttons">
        ${e.propertyValues.map((o=>this.#ze(o,e.propertyName,o===t)))}
      </div>
    </div>`}#ze(e,t,o=!1){const n=`${t}: ${e}`,r=this.findIcon(n,this.#Te);if(!r)throw new Error(`Icon for ${n} is not found`);const i=`transform: rotate(${r.rotate}deg) scale(${r.scaleX}, ${r.scaleY})`,l=mo.classMap({button:!0,selected:o}),c={propertyName:t,propertyValue:e},d=uo(o?co.deselectButton:co.selectButton,c);return go`
      <button title=${d}
              class=${l}
              jslog=${s.item().track({click:!0}).context(`${t}-${e}`)}
              @click=${()=>this.#Oe(t,e,o)}>
        <${a.Icon.Icon.litTagName} style=${i} name=${r.iconName}>
        </${a.Icon.Icon.litTagName}>
      </button>
    `}#Oe(e,t,o){o?this.dispatchEvent(new vo(e,t)):this.dispatchEvent(new yo(e,t))}findIcon(e,t){throw new Error("Not implemented")}}class bo extends fo{jslogContext="cssFlexboxEditor";editableProperties=wo;findIcon(e,t){return We(e,t)}}customElements.define("devtools-flexbox-editor",bo);class xo extends fo{jslogContext="cssGridEditor";editableProperties=So;findIcon(e,t){return Ye(e,t)}}customElements.define("devtools-grid-editor",xo);const wo=[{propertyName:"flex-direction",propertyValues:["row","column","row-reverse","column-reverse"]},{propertyName:"flex-wrap",propertyValues:["nowrap","wrap"]},{propertyName:"align-content",propertyValues:["center","flex-start","flex-end","space-around","space-between","stretch"]},{propertyName:"justify-content",propertyValues:["center","flex-start","flex-end","space-between","space-around","space-evenly"]},{propertyName:"align-items",propertyValues:["center","flex-start","flex-end","stretch","baseline"]}],So=[{propertyName:"align-content",propertyValues:["center","space-between","space-around","space-evenly","stretch"]},{propertyName:"justify-content",propertyValues:["center","start","end","space-between","space-around","space-evenly"]},{propertyName:"align-items",propertyValues:["center","start","end","stretch","baseline"]},{propertyName:"justify-items",propertyValues:["center","start","end","stretch"]}];var ko=Object.freeze({__proto__:null,PropertySelectedEvent:yo,PropertyDeselectedEvent:vo,StylePropertyEditor:fo,FlexboxEditor:bo,GridEditor:xo,FlexboxEditableProperties:wo,GridEditableProperties:So});export{w as AccessibilityTreeNode,E as AdornerManager,_ as AdornerSettingsPane,F as AnchorFunctionLinkSwatch,de as CSSHintDetailsView,fe as CSSPropertyDocsView,Ke as CSSPropertyIconResolver,nt as CSSQuery,gt as CSSVariableValueView,X as ComputedStyleProperty,te as ComputedStyleTrace,Tt as ElementsBreadcrumbs,St as ElementsBreadcrumbsUtils,It as ElementsTreeExpandButton,Pt as Helper,Jt as LayoutPane,Zt as LayoutPaneUtils,ao as QueryContainer,ko as StylePropertyEditor};
