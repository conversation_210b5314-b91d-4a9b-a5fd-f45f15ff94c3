{"version": 3, "file": "router-store.d.ts", "sourceRoot": "", "sources": ["../../src/global-state/router-store.tsx"], "names": [], "mappings": "AAEA,OAAO,EACL,iCAAiC,EACjC,eAAe,EACf,YAAY,EAEZ,eAAe,EAChB,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,aAAa,EAA6C,MAAM,OAAO,CAAC;AAGjF,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAErC,OAAO,EAAE,kBAAkB,EAAE,oBAAoB,EAAoB,MAAM,qBAAqB,CAAC;AAGjG,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAA2C,SAAS,EAAE,MAAM,aAAa,CAAC;AACjF,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAK1C,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;AACxE,MAAM,MAAM,oBAAoB,GAAG,eAAe,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;AACnF,MAAM,MAAM,iBAAiB,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,eAAe,CAAC,CAAC,CAAC;AAEhF,MAAM,MAAM,WAAW,GAAG,OAAO,KAAK,CAAC;AAsBvC,eAAO,MAAM,KAAK;;;;;oBAaA,SAAS;;;;2BAYF,iBAAiB;;;CAiDzC,CAAC;AAEF,wBAAgB,QAAQ,CACtB,OAAO,EAAE,cAAc,EACvB,oBAAoB,EAAE,oBAAoB,EAC1C,SAAS,CAAC,EAAE,MAAM;;;;;oBAlEF,SAAS;;;;2BAYF,iBAAiB;;;EAsIzC;AAUD,wBAAgB,YAAY,cAE3B"}