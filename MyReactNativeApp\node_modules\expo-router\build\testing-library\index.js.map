{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/testing-library/index.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AA8BA,oCAsCC;AApED,oBAAkB;AAClB,mBAAiB;AAEjB,gEAAkF;AAClF,kDAA0B;AAE1B,+CAAiF;AAsBrD,8FAtBA,2BAAa,OAsBA;AAAE,+FAtBA,4BAAc,OAsBA;AArBzD,0CAAuC;AAEvC,+DAA2E;AAC3E,sDAA2C;AAE3C,uBAAuB;AACvB,gEAA8C;AAiB9C,SAAgB,YAAY,CAC1B,UAA6B,OAAO,EACpC,EAAE,UAAU,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,OAAO,KAA0B,EAAE;IAEnE,IAAI,CAAC,aAAa,EAAE,CAAC;IAErB,MAAM,WAAW,GAAG,IAAA,4BAAc,EAAC,OAAO,CAAC,CAAC;IAE5C,qCAAqC;IACrC,OAAO,CAAC,GAAG,CAAC,uBAAuB,GAAG,MAAM,CAAC;IAE7C,MAAM,MAAM,GAAG,IAAA,qBAAM,EACnB,CAAC,mBAAQ,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAG,EAC1E,OAAO,CACR,CAAC;IAEF;;;;OAIG;IACH,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;QAC3B,WAAW;YACT,OAAO,oBAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;QACvC,CAAC;QACD,WAAW;YACT,OAAO,oBAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;QACvC,CAAC;QACD,eAAe;YACb,OAAO,oBAAK,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC;QACrC,CAAC;QACD,qBAAqB;YACnB,OAAO,oBAAK,CAAC,YAAY,EAAE,CAAC,kBAAkB,CAAC;QACjD,CAAC;QACD,cAAc;YACZ,OAAO,oBAAK,CAAC,KAAK,CAAC;QACrB,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAEY,QAAA,UAAU,GAAG;IACxB,yDAAyD;IACzD,QAAQ,CAAC,IAAY;QACnB,IAAA,kBAAG,EAAC,GAAG,EAAE,CAAC,uBAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACjC,MAAM,CAAC,qBAAM,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IACD,yDAAyD;IACzD,IAAI,CAAC,IAAY;QACf,IAAA,kBAAG,EAAC,GAAG,EAAE,CAAC,uBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7B,MAAM,CAAC,qBAAM,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IACD,6DAA6D;IAC7D,OAAO,CAAC,IAAY;QAClB,IAAA,kBAAG,EAAC,GAAG,EAAE,CAAC,uBAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,qBAAM,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IACD,oDAAoD;IACpD,IAAI,CAAC,IAAa;QAChB,MAAM,CAAC,uBAAM,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,IAAA,kBAAG,EAAC,GAAG,EAAE,CAAC,uBAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACzB,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,qBAAM,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,qEAAqE;IACrE,SAAS;QACP,OAAO,uBAAM,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;IACD,wEAAwE;IACxE,SAAS,CAAC,MAA8B,EAAE,IAAa;QACrD,uBAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,qBAAM,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,qEAAqE;IACrE,UAAU;QACR,IAAA,kBAAG,EAAC,GAAG,EAAE,CAAC,uBAAM,CAAC,UAAU,EAAE,CAAC,CAAC;IACjC,CAAC;CACF,CAAC", "sourcesContent": ["import './expect';\nimport './mocks';\n\nimport { act, render, RenderResult, screen } from '@testing-library/react-native';\nimport React from 'react';\n\nimport { MockContextConfig, getMockConfig, getMockContext } from './mock-config';\nimport { ExpoRoot } from '../ExpoRoot';\nimport { ExpoLinkingOptions } from '../getLinkingConfig';\nimport { ReactNavigationState, store } from '../global-state/router-store';\nimport { router } from '../imperative-api';\n\n// re-export everything\nexport * from '@testing-library/react-native';\n\nexport type RenderRouterOptions = Parameters<typeof render>[1] & {\n  initialUrl?: any;\n  linking?: Partial<ExpoLinkingOptions>;\n};\n\ntype Result = ReturnType<typeof render> & {\n  getPathname(): string;\n  getPathnameWithParams(): string;\n  getSegments(): string[];\n  getSearchParams(): Record<string, string | string[]>;\n  getRouterState(): ReactNavigationState | undefined;\n};\n\nexport { MockContextConfig, getMockConfig, getMockContext };\n\nexport function renderRouter(\n  context: MockContextConfig = './app',\n  { initialUrl = '/', linking, ...options }: RenderRouterOptions = {}\n): Result {\n  jest.useFakeTimers();\n\n  const mockContext = getMockContext(context);\n\n  // Force the render to be synchronous\n  process.env.EXPO_ROUTER_IMPORT_MODE = 'sync';\n\n  const result = render(\n    <ExpoRoot context={mockContext} location={initialUrl} linking={linking} />,\n    options\n  );\n\n  /**\n   * This is a hack to ensure that React Navigation's state updates are processed before we run assertions.\n   * Some updates are async and we need to wait for them to complete, otherwise will we get a false positive.\n   * (that the app will briefly be in the right state, but then update to an invalid state)\n   */\n  return Object.assign(result, {\n    getPathname(this: RenderResult): string {\n      return store.getRouteInfo().pathname;\n    },\n    getSegments(this: RenderResult): string[] {\n      return store.getRouteInfo().segments;\n    },\n    getSearchParams(this: RenderResult): Record<string, string | string[]> {\n      return store.getRouteInfo().params;\n    },\n    getPathnameWithParams(this: RenderResult): string {\n      return store.getRouteInfo().pathnameWithParams;\n    },\n    getRouterState(this: RenderResult) {\n      return store.state;\n    },\n  });\n}\n\nexport const testRouter = {\n  /** Navigate to the provided pathname and the pathname */\n  navigate(path: string) {\n    act(() => router.navigate(path));\n    expect(screen).toHavePathnameWithParams(path);\n  },\n  /** Push the provided pathname and assert the pathname */\n  push(path: string) {\n    act(() => router.push(path));\n    expect(screen).toHavePathnameWithParams(path);\n  },\n  /** Replace with provided pathname and assert the pathname */\n  replace(path: string) {\n    act(() => router.replace(path));\n    expect(screen).toHavePathnameWithParams(path);\n  },\n  /** Go back in history and asset the new pathname */\n  back(path?: string) {\n    expect(router.canGoBack()).toBe(true);\n    act(() => router.back());\n    if (path) {\n      expect(screen).toHavePathnameWithParams(path);\n    }\n  },\n  /** If there's history that supports invoking the `back` function. */\n  canGoBack() {\n    return router.canGoBack();\n  },\n  /** Update the current route query params and assert the new pathname */\n  setParams(params: Record<string, string>, path?: string) {\n    router.setParams(params);\n    if (path) {\n      expect(screen).toHavePathnameWithParams(path);\n    }\n  },\n  /** If there's history that supports invoking the `back` function. */\n  dismissAll() {\n    act(() => router.dismissAll());\n  },\n};\n"]}