import*as e from"../../../../core/i18n/i18n.js";import{assertNotNullOrUndefined as t}from"../../../../core/platform/platform.js";import*as r from"../../../../core/sdk/sdk.js";import*as a from"../../../../third_party/diff/diff.js";import*as i from"../../../../ui/components/data_grid/data_grid.js";import*as n from"../../../../ui/components/legacy_wrapper/legacy_wrapper.js";import*as o from"../../../../ui/lit-html/lit-html.js";import*as s from"../../../../models/bindings/bindings.js";import*as d from"../../../../core/common/common.js";import*as l from"../../../../models/logs/logs.js";import*as c from"../../../../ui/components/buttons/buttons.js";import*as u from"../../../../ui/components/render_coordinator/render_coordinator.js";import*as p from"../../../../ui/components/report_view/report_view.js";import*as g from"../../../../ui/components/request_link_icon/request_link_icon.js";import*as h from"../../../../ui/legacy/legacy.js";import*as m from"../../../../ui/visual_logging/visual_logging.js";import*as b from"../helper/helper.js";import*as f from"../../../../ui/components/chrome_link/chrome_link.js";import*as v from"../../../../ui/components/dialogs/dialogs.js";import*as S from"../../../../ui/components/icon_button/icon_button.js";import*as R from"../../../../third_party/codemirror.next/codemirror.next.js";import*as w from"../../../../ui/components/code_highlighter/code_highlighter.js";import*as P from"../../../../ui/components/text_editor/text_editor.js";import*as y from"../../../network/forward/forward.js";const N={PrefetchFailedIneligibleRedirect:"The prefetch was redirected, but the redirect URL is not eligible for prefetch.",PrefetchFailedInvalidRedirect:"The prefetch was redirected, but there was a problem with the redirect.",PrefetchFailedMIMENotSupported:"The prefetch failed because the response's Content-Type header was not supported.",PrefetchFailedNetError:"The prefetch failed because of a network error.",PrefetchFailedNon2XX:"The prefetch failed because of a non-2xx HTTP response status code.",PrefetchFailedPerPageLimitExceeded:"The prefetch was not performed because the initiating page already has too many prefetches ongoing.",PrefetchIneligibleRetryAfter:"A previous prefetch to the origin got a HTTP 503 response with an Retry-After header that has not elapsed yet.",PrefetchIsPrivacyDecoy:"The URL was not eligible to be prefetched because there was a registered service worker or cross-site cookies for that origin, but the prefetch was put on the network anyways and not used, to disguise that the user had some kind of previous relationship with the origin.",PrefetchIsStale:"Too much time elapsed between the prefetch and usage, so the prefetch was discarded.",PrefetchNotEligibleBrowserContextOffTheRecord:"The prefetch was not performed because the browser is in Incognito or Guest mode.",PrefetchNotEligibleDataSaverEnabled:"The prefetch was not performed because the operating system is in Data Saver mode.",PrefetchNotEligibleExistingProxy:"The URL is not eligible to be prefetched, because in the default network context it is configured to use a proxy server.",PrefetchNotEligibleHostIsNonUnique:"The URL was not eligible to be prefetched because its host was not unique (e.g., a non publicly routable IP address or a hostname which is not registry-controlled), but the prefetch was required to be proxied.",PrefetchNotEligibleNonDefaultStoragePartition:"The URL was not eligible to be prefetched because it uses a non-default storage partition.",PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy:"The URL was not eligible to be prefetched because the default network context cannot be configured to use the prefetch proxy for a same-site cross-origin prefetch request.",PrefetchNotEligibleSchemeIsNotHttps:"The URL was not eligible to be prefetched because its scheme was not https:.",PrefetchNotEligibleUserHasCookies:"The URL was not eligible to be prefetched because it was cross-site, but the user had cookies for that origin.",PrefetchNotEligibleUserHasServiceWorker:"The URL was not eligible to be prefetched because there was a registered service worker for that origin, which is currently not supported.",PrefetchNotUsedCookiesChanged:"The prefetch was not used because it was a cross-site prefetch, and cookies were added for that URL while the prefetch was ongoing, so the prefetched response is now out-of-date.",PrefetchProxyNotAvailable:"A network error was encountered when trying to set up a connection to the prefetching proxy.",PrefetchNotUsedProbeFailed:"The prefetch was blocked by your Internet Service Provider or network administrator.",PrefetchEvictedForNewerPrefetch:"The prefetch was discarded because the initiating page has too many prefetches ongoing, and this was one of the oldest.",PrefetchEvictedAfterCandidateRemoved:"The prefetch was discarded because no speculation rule in the initating page triggers a prefetch for this URL anymore.",PrefetchNotEligibleBatterySaverEnabled:"The prefetch was not performed because the Battery Saver setting was enabled.",PrefetchNotEligiblePreloadingDisabled:"The prefetch was not performed because speculative loading was disabled.",prerenderFinalStatusLowEndDevice:"The prerender was not performed because this device does not have enough total system memory to support prerendering.",prerenderFinalStatusInvalidSchemeRedirect:"The prerendering navigation failed because it redirected to a URL whose scheme was not http: or https:.",prerenderFinalStatusInvalidSchemeNavigation:"The URL was not eligible to be prerendered because its scheme was not http: or https:.",prerenderFinalStatusNavigationRequestBlockedByCsp:"The prerendering navigation was blocked by a Content Security Policy.",prerenderFinalStatusMainFrameNavigation:"The prerendered page navigated itself to another URL, which is currently not supported.",prerenderFinalStatusMojoBinderPolicy:"The prerendered page used a forbidden JavaScript API that is currently not supported. (Internal Mojo interface: {PH1})",prerenderFinalStatusRendererProcessCrashed:"The prerendered page crashed.",prerenderFinalStatusRendererProcessKilled:"The prerendered page was killed.",prerenderFinalStatusDownload:"The prerendered page attempted to initiate a download, which is currently not supported.",prerenderFinalStatusNavigationBadHttpStatus:"The prerendering navigation failed because of a non-2xx HTTP response status code.",prerenderFinalStatusClientCertRequested:"The prerendering navigation required a HTTP client certificate.",prerenderFinalStatusNavigationRequestNetworkError:"The prerendering navigation encountered a network error.",prerenderFinalStatusSslCertificateError:"The prerendering navigation failed because of an invalid SSL certificate.",prerenderFinalStatusLoginAuthRequested:"The prerendering navigation required HTTP authentication, which is currently not supported.",prerenderFinalStatusUaChangeRequiresReload:"Changing User Agent occured in prerendering navigation.",prerenderFinalStatusBlockedByClient:"Some resource load was blocked.",prerenderFinalStatusAudioOutputDeviceRequested:"The prerendered page requested audio output, which is currently not supported.",prerenderFinalStatusMixedContent:"The prerendered page contained mixed content.",prerenderFinalStatusTriggerBackgrounded:"The initiating page was backgrounded, so the prerendered page was discarded.",prerenderFinalStatusMemoryLimitExceeded:"The prerender was not performed because the browser exceeded the prerendering memory limit.",prerenderFinalStatusDataSaverEnabled:"The prerender was not performed because the user requested that the browser use less data.",prerenderFinalStatusHasEffectiveUrl:"The initiating page cannot perform prerendering, because it has an effective URL that is different from its normal URL. (For example, the New Tab Page, or hosted apps.)",prerenderFinalStatusTimeoutBackgrounded:"The initiating page was backgrounded for a long time, so the prerendered page was discarded.",prerenderFinalStatusCrossSiteRedirectInInitialNavigation:"The prerendering navigation failed because the prerendered URL redirected to a cross-site URL.",prerenderFinalStatusCrossSiteNavigationInInitialNavigation:"The prerendering navigation failed because it targeted a cross-site URL.",prerenderFinalStatusSameSiteCrossOriginRedirectNotOptInInInitialNavigation:"The prerendering navigation failed because the prerendered URL redirected to a cross-origin same-site URL, but the destination response did not include the appropriate Supports-Loading-Mode header.",prerenderFinalStatusSameSiteCrossOriginNavigationNotOptInInInitialNavigation:"The prerendering navigation failed because it was to a cross-origin same-site URL, but the destination response did not include the appropriate Supports-Loading-Mode header.",prerenderFinalStatusActivationNavigationParameterMismatch:"The prerender was not used because during activation time, different navigation parameters (e.g., HTTP headers) were calculated than during the original prerendering navigation request.",prerenderFinalStatusPrimaryMainFrameRendererProcessCrashed:"The initiating page crashed.",prerenderFinalStatusPrimaryMainFrameRendererProcessKilled:"The initiating page was killed.",prerenderFinalStatusActivationFramePolicyNotCompatible:"The prerender was not used because the sandboxing flags or permissions policy of the initiating page was not compatible with those of the prerendering page.",prerenderFinalStatusPreloadingDisabled:"The prerender was not performed because the user disabled preloading in their browser settings.",prerenderFinalStatusBatterySaverEnabled:"The prerender was not performed because the user requested that the browser use less battery.",prerenderFinalStatusActivatedDuringMainFrameNavigation:"Prerendered page activated during initiating page's main frame navigation.",prerenderFinalStatusCrossSiteRedirectInMainFrameNavigation:"The prerendered page navigated to a URL which redirected to a cross-site URL.",prerenderFinalStatusCrossSiteNavigationInMainFrameNavigation:"The prerendered page navigated to a cross-site URL.",prerenderFinalStatusSameSiteCrossOriginRedirectNotOptInInMainFrameNavigation:"The prerendered page navigated to a URL which redirected to a cross-origin same-site URL, but the destination response did not include the appropriate Supports-Loading-Mode header.",prerenderFinalStatusSameSiteCrossOriginNavigationNotOptInInMainFrameNavigation:"The prerendered page navigated to a cross-origin same-site URL, but the destination response did not include the appropriate Supports-Loading-Mode header.",prerenderFinalStatusMemoryPressureOnTrigger:"The prerender was not performed because the browser was under critical memory pressure.",prerenderFinalStatusMemoryPressureAfterTriggered:"The prerendered page was unloaded because the browser came under critical memory pressure.",prerenderFinalStatusPrerenderingDisabledByDevTools:"The prerender was not performed because DevTools has been used to disable prerendering.",prerenderFinalStatusSpeculationRuleRemoved:'The prerendered page was unloaded because the initiating page removed the corresponding prerender rule from <script type="speculationrules">.',prerenderFinalStatusActivatedWithAuxiliaryBrowsingContexts:"The prerender was not used because during activation time, there were other windows with an active opener reference to the initiating page, which is currently not supported.",prerenderFinalStatusMaxNumOfRunningEagerPrerendersExceeded:'The prerender whose eagerness is "eager" was not performed because the initiating page already has too many prerenders ongoing. Remove other speculation rules with "eager" to enable further prerendering.',prerenderFinalStatusMaxNumOfRunningEmbedderPrerendersExceeded:"The browser-triggered prerender was not performed because the initiating page already has too many prerenders ongoing.",prerenderFinalStatusMaxNumOfRunningNonEagerPrerendersExceeded:'The old non-eager prerender (with a "moderate" or "conservative" eagerness and triggered by hovering or clicking links) was automatically canceled due to starting a new non-eager prerender. It can be retriggered by interacting with the link again.',prerenderFinalStatusPrerenderingUrlHasEffectiveUrl:"The prerendering navigation failed because it has an effective URL that is different from its normal URL. (For example, the New Tab Page, or hosted apps.)",prerenderFinalStatusRedirectedPrerenderingUrlHasEffectiveUrl:"The prerendering navigation failed because it redirected to an effective URL that is different from its normal URL. (For example, the New Tab Page, or hosted apps.)",prerenderFinalStatusActivationUrlHasEffectiveUrl:"The prerender was not used because during activation time, navigation has an effective URL that is different from its normal URL. (For example, the New Tab Page, or hosted apps.)",prerenderFinalStatusJavaScriptInterfaceAdded:"The prerendered page was unloaded because a new JavaScript interface has been injected by WebView.addJavascriptInterface().",prerenderFinalStatusJavaScriptInterfaceRemoved:"The prerendered page was unloaded because a JavaScript interface has been removed by WebView.removeJavascriptInterface().",prerenderFinalStatusAllPrerenderingCanceled:"All prerendered pages were unloaded by the browser for some reason (For example, WebViewCompat.addWebMessageListener() was called during prerendering.)",statusNotTriggered:"Not triggered",statusPending:"Pending",statusRunning:"Running",statusReady:"Ready",statusSuccess:"Success",statusFailure:"Failure"},T=e.i18n.registerUIStrings("panels/application/preloading/components/PreloadingString.ts",N),F=e.i18n.getLazilyComputedLocalizedString.bind(void 0,T),k=e.i18n.getLocalizedString.bind(void 0,T),$={PrefetchFailedIneligibleRedirect:{name:F(N.PrefetchFailedIneligibleRedirect)},PrefetchFailedInvalidRedirect:{name:F(N.PrefetchFailedInvalidRedirect)},PrefetchFailedMIMENotSupported:{name:F(N.PrefetchFailedMIMENotSupported)},PrefetchFailedNetError:{name:F(N.PrefetchFailedNetError)},PrefetchFailedNon2XX:{name:F(N.PrefetchFailedNon2XX)},PrefetchFailedPerPageLimitExceeded:{name:F(N.PrefetchFailedPerPageLimitExceeded)},PrefetchIneligibleRetryAfter:{name:F(N.PrefetchIneligibleRetryAfter)},PrefetchIsPrivacyDecoy:{name:F(N.PrefetchIsPrivacyDecoy)},PrefetchIsStale:{name:F(N.PrefetchIsStale)},PrefetchNotEligibleBrowserContextOffTheRecord:{name:F(N.PrefetchNotEligibleBrowserContextOffTheRecord)},PrefetchNotEligibleDataSaverEnabled:{name:F(N.PrefetchNotEligibleDataSaverEnabled)},PrefetchNotEligibleExistingProxy:{name:F(N.PrefetchNotEligibleExistingProxy)},PrefetchNotEligibleHostIsNonUnique:{name:F(N.PrefetchNotEligibleHostIsNonUnique)},PrefetchNotEligibleNonDefaultStoragePartition:{name:F(N.PrefetchNotEligibleNonDefaultStoragePartition)},PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy:{name:F(N.PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy)},PrefetchNotEligibleSchemeIsNotHttps:{name:F(N.PrefetchNotEligibleSchemeIsNotHttps)},PrefetchNotEligibleUserHasCookies:{name:F(N.PrefetchNotEligibleUserHasCookies)},PrefetchNotEligibleUserHasServiceWorker:{name:F(N.PrefetchNotEligibleUserHasServiceWorker)},PrefetchNotUsedCookiesChanged:{name:F(N.PrefetchNotUsedCookiesChanged)},PrefetchProxyNotAvailable:{name:F(N.PrefetchProxyNotAvailable)},PrefetchNotUsedProbeFailed:{name:F(N.PrefetchNotUsedProbeFailed)},PrefetchEvictedForNewerPrefetch:{name:F(N.PrefetchEvictedForNewerPrefetch)},PrefetchEvictedAfterCandidateRemoved:{name:F(N.PrefetchEvictedAfterCandidateRemoved)},PrefetchNotEligibleBatterySaverEnabled:{name:F(N.PrefetchNotEligibleBatterySaverEnabled)},PrefetchNotEligiblePreloadingDisabled:{name:F(N.PrefetchNotEligiblePreloadingDisabled)}};function I({prefetchStatus:t}){switch(t){case null:case"PrefetchNotStarted":case"PrefetchNotFinishedInTime":case"PrefetchResponseUsed":case"PrefetchAllowed":case"PrefetchHeldback":case"PrefetchSuccessfulButNotUsed":return null;case"PrefetchFailedIneligibleRedirect":return $.PrefetchFailedIneligibleRedirect.name();case"PrefetchFailedInvalidRedirect":return $.PrefetchFailedInvalidRedirect.name();case"PrefetchFailedMIMENotSupported":return $.PrefetchFailedMIMENotSupported.name();case"PrefetchFailedNetError":return $.PrefetchFailedNetError.name();case"PrefetchFailedNon2XX":return $.PrefetchFailedNon2XX.name();case"PrefetchFailedPerPageLimitExceeded":return $.PrefetchFailedPerPageLimitExceeded.name();case"PrefetchIneligibleRetryAfter":return $.PrefetchIneligibleRetryAfter.name();case"PrefetchEvictedForNewerPrefetch":return $.PrefetchEvictedForNewerPrefetch.name();case"PrefetchEvictedAfterCandidateRemoved":return $.PrefetchEvictedAfterCandidateRemoved.name();case"PrefetchIsPrivacyDecoy":return $.PrefetchIsPrivacyDecoy.name();case"PrefetchIsStale":return $.PrefetchIsStale.name();case"PrefetchNotEligibleBrowserContextOffTheRecord":return $.PrefetchNotEligibleBrowserContextOffTheRecord.name();case"PrefetchNotEligibleDataSaverEnabled":return $.PrefetchNotEligibleDataSaverEnabled.name();case"PrefetchNotEligibleExistingProxy":return $.PrefetchNotEligibleExistingProxy.name();case"PrefetchNotEligibleHostIsNonUnique":return $.PrefetchNotEligibleHostIsNonUnique.name();case"PrefetchNotEligibleNonDefaultStoragePartition":return $.PrefetchNotEligibleNonDefaultStoragePartition.name();case"PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy":return $.PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy.name();case"PrefetchNotEligibleSchemeIsNotHttps":return $.PrefetchNotEligibleSchemeIsNotHttps.name();case"PrefetchNotEligibleUserHasCookies":return $.PrefetchNotEligibleUserHasCookies.name();case"PrefetchNotEligibleUserHasServiceWorker":return $.PrefetchNotEligibleUserHasServiceWorker.name();case"PrefetchNotUsedCookiesChanged":return $.PrefetchNotUsedCookiesChanged.name();case"PrefetchProxyNotAvailable":return $.PrefetchProxyNotAvailable.name();case"PrefetchNotUsedProbeFailed":return $.PrefetchNotUsedProbeFailed.name();case"PrefetchNotEligibleBatterySaverEnabled":return $.PrefetchNotEligibleBatterySaverEnabled.name();case"PrefetchNotEligiblePreloadingDisabled":return $.PrefetchNotEligiblePreloadingDisabled.name();default:return e.i18n.lockedString(`Unknown failure reason: ${t}`)}}function C(r){switch(r.prerenderStatus){case null:case"Activated":return null;case"Destroyed":case"DidFailLoad":case"Stop":return e.i18n.lockedString("Unknown");case"LowEndDevice":return k(N.prerenderFinalStatusLowEndDevice);case"InvalidSchemeRedirect":return k(N.prerenderFinalStatusInvalidSchemeRedirect);case"InvalidSchemeNavigation":return k(N.prerenderFinalStatusInvalidSchemeNavigation);case"NavigationRequestBlockedByCsp":return k(N.prerenderFinalStatusNavigationRequestBlockedByCsp);case"MainFrameNavigation":return k(N.prerenderFinalStatusMainFrameNavigation);case"MojoBinderPolicy":return t(r.disallowedMojoInterface),k(N.prerenderFinalStatusMojoBinderPolicy,{PH1:r.disallowedMojoInterface});case"RendererProcessCrashed":return k(N.prerenderFinalStatusRendererProcessCrashed);case"RendererProcessKilled":return k(N.prerenderFinalStatusRendererProcessKilled);case"Download":return k(N.prerenderFinalStatusDownload);case"TriggerDestroyed":case"NavigationNotCommitted":case"ActivatedBeforeStarted":case"InactivePageRestriction":case"StartFailed":case"ActivatedInBackground":case"ActivationNavigationDestroyedBeforeSuccess":return e.i18n.lockedString("Internal error");case"NavigationBadHttpStatus":return k(N.prerenderFinalStatusNavigationBadHttpStatus);case"ClientCertRequested":return k(N.prerenderFinalStatusClientCertRequested);case"NavigationRequestNetworkError":return k(N.prerenderFinalStatusNavigationRequestNetworkError);case"CancelAllHostsForTesting":case"EmbedderHostDisallowed":case"TabClosedByUserGesture":case"TabClosedWithoutUserGesture":case"PreloadingUnsupportedByWebContents":throw new Error("unreachable");case"SslCertificateError":return k(N.prerenderFinalStatusSslCertificateError);case"LoginAuthRequested":return k(N.prerenderFinalStatusLoginAuthRequested);case"UaChangeRequiresReload":return k(N.prerenderFinalStatusUaChangeRequiresReload);case"BlockedByClient":return k(N.prerenderFinalStatusBlockedByClient);case"AudioOutputDeviceRequested":return k(N.prerenderFinalStatusAudioOutputDeviceRequested);case"MixedContent":return k(N.prerenderFinalStatusMixedContent);case"TriggerBackgrounded":return k(N.prerenderFinalStatusTriggerBackgrounded);case"MemoryLimitExceeded":return k(N.prerenderFinalStatusMemoryLimitExceeded);case"DataSaverEnabled":return k(N.prerenderFinalStatusDataSaverEnabled);case"TriggerUrlHasEffectiveUrl":return k(N.prerenderFinalStatusHasEffectiveUrl);case"TimeoutBackgrounded":return k(N.prerenderFinalStatusTimeoutBackgrounded);case"CrossSiteRedirectInInitialNavigation":return k(N.prerenderFinalStatusCrossSiteRedirectInInitialNavigation);case"CrossSiteNavigationInInitialNavigation":return k(N.prerenderFinalStatusCrossSiteNavigationInInitialNavigation);case"SameSiteCrossOriginRedirectNotOptInInInitialNavigation":return k(N.prerenderFinalStatusSameSiteCrossOriginRedirectNotOptInInInitialNavigation);case"SameSiteCrossOriginNavigationNotOptInInInitialNavigation":return k(N.prerenderFinalStatusSameSiteCrossOriginNavigationNotOptInInInitialNavigation);case"ActivationNavigationParameterMismatch":return k(N.prerenderFinalStatusActivationNavigationParameterMismatch);case"PrimaryMainFrameRendererProcessCrashed":return k(N.prerenderFinalStatusPrimaryMainFrameRendererProcessCrashed);case"PrimaryMainFrameRendererProcessKilled":return k(N.prerenderFinalStatusPrimaryMainFrameRendererProcessKilled);case"ActivationFramePolicyNotCompatible":return k(N.prerenderFinalStatusActivationFramePolicyNotCompatible);case"PreloadingDisabled":return k(N.prerenderFinalStatusPreloadingDisabled);case"BatterySaverEnabled":return k(N.prerenderFinalStatusBatterySaverEnabled);case"ActivatedDuringMainFrameNavigation":return k(N.prerenderFinalStatusActivatedDuringMainFrameNavigation);case"CrossSiteRedirectInMainFrameNavigation":return k(N.prerenderFinalStatusCrossSiteRedirectInMainFrameNavigation);case"CrossSiteNavigationInMainFrameNavigation":return k(N.prerenderFinalStatusCrossSiteNavigationInMainFrameNavigation);case"SameSiteCrossOriginRedirectNotOptInInMainFrameNavigation":return k(N.prerenderFinalStatusSameSiteCrossOriginRedirectNotOptInInMainFrameNavigation);case"SameSiteCrossOriginNavigationNotOptInInMainFrameNavigation":return k(N.prerenderFinalStatusSameSiteCrossOriginNavigationNotOptInInMainFrameNavigation);case"MemoryPressureOnTrigger":return k(N.prerenderFinalStatusMemoryPressureOnTrigger);case"MemoryPressureAfterTriggered":return k(N.prerenderFinalStatusMemoryPressureAfterTriggered);case"PrerenderingDisabledByDevTools":return k(N.prerenderFinalStatusPrerenderingDisabledByDevTools);case"SpeculationRuleRemoved":return k(N.prerenderFinalStatusSpeculationRuleRemoved);case"ActivatedWithAuxiliaryBrowsingContexts":return k(N.prerenderFinalStatusActivatedWithAuxiliaryBrowsingContexts);case"MaxNumOfRunningEagerPrerendersExceeded":return k(N.prerenderFinalStatusMaxNumOfRunningEagerPrerendersExceeded);case"MaxNumOfRunningEmbedderPrerendersExceeded":return k(N.prerenderFinalStatusMaxNumOfRunningEmbedderPrerendersExceeded);case"MaxNumOfRunningNonEagerPrerendersExceeded":return k(N.prerenderFinalStatusMaxNumOfRunningNonEagerPrerendersExceeded);case"PrerenderingUrlHasEffectiveUrl":return k(N.prerenderFinalStatusPrerenderingUrlHasEffectiveUrl);case"RedirectedPrerenderingUrlHasEffectiveUrl":return k(N.prerenderFinalStatusRedirectedPrerenderingUrlHasEffectiveUrl);case"ActivationUrlHasEffectiveUrl":return k(N.prerenderFinalStatusActivationUrlHasEffectiveUrl);case"JavaScriptInterfaceAdded":return k(N.prerenderFinalStatusJavaScriptInterfaceAdded);case"JavaScriptInterfaceRemoved":return k(N.prerenderFinalStatusJavaScriptInterfaceRemoved);case"AllPrerenderingCanceled":return k(N.prerenderFinalStatusAllPrerenderingCanceled);case"WindowClosed":return"";default:return e.i18n.lockedString(`Unknown failure reason: ${r.prerenderStatus}`)}}function x(e,t){const r=void 0===e.url?t:e.url;return s.ResourceUtils.displayNameForURL(r)}function E(t){switch(t){case"Prefetch":return e.i18n.lockedString("Prefetch");case"Prerender":return e.i18n.lockedString("Prerender")}}function D(r){const a=function(t){switch(t){case"NotTriggered":return k(N.statusNotTriggered);case"Pending":return k(N.statusPending);case"Running":return k(N.statusRunning);case"Ready":return k(N.statusReady);case"Success":return k(N.statusSuccess);case"Failure":return k(N.statusFailure);case"NotSupported":return e.i18n.lockedString("Internal error")}}(r.status);if("Failure"!==r.status)return a;switch(r.action){case"Prefetch":return a+" - "+(I(r)??e.i18n.lockedString("Internal error"));case"Prerender":{const e=C(r);return t(e),a+" - "+e}}}const U={url:"URL",action:"Action",status:"Status",statusNotTriggered:"Not triggered",statusPending:"Pending",statusRunning:"Running",statusReady:"Ready",statusSuccess:"Success",statusFailure:"Failure"},L=e.i18n.registerUIStrings("panels/application/preloading/components/MismatchedPreloadingGrid.ts",U),M=e.i18n.getLocalizedString.bind(void 0,L);class B{static status(t){switch(t){case"NotTriggered":return M(U.statusNotTriggered);case"Pending":return M(U.statusPending);case"Running":return M(U.statusRunning);case"Ready":return M(U.statusReady);case"Success":return M(U.statusSuccess);case"Failure":return M(U.statusFailure);case"NotSupported":return e.i18n.lockedString("Internal error")}}}const{render:V,html:H}=o;class A extends n.LegacyWrapper.WrappableComponent{static litTagName=o.literal`devtools-resources-mismatched-preloading-grid`;#e=this.attachShadow({mode:"open"});#t=null;connectedCallback(){this.#e.adoptedStyleSheets=[],this.#r()}set data(e){this.#t=e,this.#r()}#r(){if(null===this.#t)return;const e={columns:[{id:"url",title:M(U.url),widthWeighting:40,hideable:!1,visible:!0,sortable:!0},{id:"action",title:M(U.action),widthWeighting:15,hideable:!1,visible:!0,sortable:!0},{id:"status",title:M(U.status),widthWeighting:15,hideable:!1,visible:!0,sortable:!0}],rows:this.#a(),striped:!0};V(H`
      <${i.DataGridController.DataGridController.litTagName} .data=${e}>
      </${i.DataGridController.DataGridController.litTagName}>
    `,this.#e,{host:this})}#a(){t(this.#t);const e=this.#t.pageURL;return this.#t.rows.map((t=>({row:t,diffScore:a.Diff.DiffWrapper.characterScore(t.url,e)}))).sort(((e,t)=>t.diffScore-e.diffScore)).map((({row:t})=>({cells:[{columnId:"url",value:t.url,renderer:()=>function(e,t){function r(e,t){return o.html`<span style=${o.Directives.styleMap(e)}>${t}</span>`}const i=a.Diff.DiffWrapper.charDiff(e,t).map((e=>{const t=e[1];switch(e[0]){case a.Diff.Operation.Equal:return r({},t);case a.Diff.Operation.Insert:return r({color:"var(--sys-color-green)","text-decoration":"line-through"},t);case a.Diff.Operation.Delete:return r({color:"var(--sys-color-error)"},t);case a.Diff.Operation.Edit:return r({color:"var(--sys-color-green)","text-decoration":"line-through"},t);default:throw new Error("unreachable")}}),o.nothing);return o.html`<div>${i}</div>`}(t.url,e)},{columnId:"action",value:E(t.action)},{columnId:"status",value:B.status(t.status)}]})))}}customElements.define("devtools-resources-mismatched-preloading-grid",A);var O=Object.freeze({__proto__:null,i18nString:M,MismatchedPreloadingGrid:A});const W=new CSSStyleSheet;W.replaceSync("button.link{color:var(--sys-color-primary);text-decoration:underline;padding:0;border:none;background:none;font-family:inherit;font-size:inherit;height:16px}button.link devtools-icon{vertical-align:sub}.link{color:var(--sys-color-primary);text-decoration:underline;cursor:pointer}\n/*# sourceURL=preloadingDetailsReportView.css */\n");const q={selectAnElementForMoreDetails:"Select an element for more details",detailsDetailedInformation:"Detailed information",detailsAction:"Action",detailsStatus:"Status",detailsFailureReason:"Failure reason",detailsRuleSet:"Rule set",detailedStatusNotTriggered:"Speculative load attempt is not yet triggered.",detailedStatusPending:"Speculative load attempt is eligible but pending.",detailedStatusRunning:"Speculative load is running.",detailedStatusReady:"Speculative load finished and the result is ready for the next navigation.",detailedStatusSuccess:"Speculative load finished and used for a navigation.",detailedStatusFailure:"Speculative load failed.",buttonInspect:"Inspect",buttonClickToInspect:"Click to inspect prerendered page",buttonClickToRevealRuleSet:"Click to reveal rule set"},_=e.i18n.registerUIStrings("panels/application/preloading/components/PreloadingDetailsReportView.ts",q),j=e.i18n.getLocalizedString.bind(void 0,_);class G{static detailedStatus({status:t}){switch(t){case"NotTriggered":return j(q.detailedStatusNotTriggered);case"Pending":return j(q.detailedStatusPending);case"Running":return j(q.detailedStatusRunning);case"Ready":return j(q.detailedStatusReady);case"Success":return j(q.detailedStatusSuccess);case"Failure":return j(q.detailedStatusFailure);case"NotSupported":return e.i18n.lockedString("Internal error")}}}const z=u.RenderCoordinator.RenderCoordinator.instance();class K extends n.LegacyWrapper.WrappableComponent{static litTagName=o.literal`devtools-resources-preloading-details-report-view`;#e=this.attachShadow({mode:"open"});#t=null;connectedCallback(){this.#e.adoptedStyleSheets=[W]}set data(e){this.#t=e,this.#r()}async#r(){await z.write("PreloadingDetailsReportView render",(()=>{if(null===this.#t)return void o.render(o.html`
          <div class="preloading-noselected">
            <div>
              <p>${j(q.selectAnElementForMoreDetails)}</p>
            </div>
          </div>
        `,this.#e,{host:this});const e=G.detailedStatus(this.#t.preloadingAttempt),t=this.#t.pageURL;o.render(o.html`
        <${p.ReportView.Report.litTagName} .data=${{reportTitle:"Speculative Loading Attempt"}}
        jslog=${m.section("preloading-details")}>
          <${p.ReportView.ReportSectionHeader.litTagName}>${j(q.detailsDetailedInformation)}</${p.ReportView.ReportSectionHeader.litTagName}>

          ${this.#i()}
          ${this.#n()}

          <${p.ReportView.ReportKey.litTagName}>${j(q.detailsStatus)}</${p.ReportView.ReportKey.litTagName}>
          <${p.ReportView.ReportValue.litTagName}>
            ${e}
          </${p.ReportView.ReportValue.litTagName}>

          ${this.#o()}
          ${this.#s()}

          ${this.#t.ruleSets.map((e=>this.#d(e,t)))}
        </${p.ReportView.Report.litTagName}>
      `,this.#e,{host:this})}))}#i(){t(this.#t);const r=this.#t.preloadingAttempt;let a;return a="Prefetch"===r.action&&void 0!==r.requestId?o.html`
          <${g.RequestLinkIcon.RequestLinkIcon.litTagName}
            .data=${{affectedRequest:{requestId:r.requestId,url:r.key.url},requestResolver:this.#t.requestResolver||new l.RequestResolver.RequestResolver,displayURL:!0,urlToDisplay:r.key.url}}
          >
          </${g.RequestLinkIcon.RequestLinkIcon.litTagName}>
      `:o.html`
          <div class="text-ellipsis" title=${r.key.url}>${r.key.url}</div>
      `,o.html`
        <${p.ReportView.ReportKey.litTagName}>${e.i18n.lockedString("URL")}</${p.ReportView.ReportKey.litTagName}>
        <${p.ReportView.ReportValue.litTagName}>
          ${a}
        </${p.ReportView.ReportValue.litTagName}>
    `}#n(){t(this.#t);const e=this.#t.preloadingAttempt,a=E(this.#t.preloadingAttempt.action);let i=o.nothing;return(()=>{if("Prerender"!==e.action)return;if(null===r.TargetManager.TargetManager.instance().primaryPageTarget())return;const t=r.TargetManager.TargetManager.instance().targets().find((t=>"prerender"===t.targetInfo()?.subtype&&t.inspectedURL()===e.key.url)),a=void 0===t;i=o.html`
          <${c.Button.Button.litTagName}
            @click=${()=>{void 0!==t&&h.Context.Context.instance().setFlavor(r.Target.Target,t)}}
            .title=${j(q.buttonClickToInspect)}
            .size=${"SMALL"}
            .variant=${"outlined"}
            .disabled=${a}
            jslog=${m.action("inspect-prerendered-page").track({click:!0})}
          >
            ${j(q.buttonInspect)}
          </${c.Button.Button.litTagName}>
      `})(),o.html`
        <${p.ReportView.ReportKey.litTagName}>${j(q.detailsAction)}</${p.ReportView.ReportKey.litTagName}>
        <${p.ReportView.ReportValue.litTagName}>
          <div class="text-ellipsis" title="">
            ${a}
            ${i}
          </div>
        </${p.ReportView.ReportValue.litTagName}>
    `}#o(){t(this.#t);const e=this.#t.preloadingAttempt;if("Prefetch"!==e.action)return o.nothing;const r=I(e);return null===r?o.nothing:o.html`
        <${p.ReportView.ReportKey.litTagName}>${j(q.detailsFailureReason)}</${p.ReportView.ReportKey.litTagName}>
        <${p.ReportView.ReportValue.litTagName}>
          ${r}
        </${p.ReportView.ReportValue.litTagName}>
    `}#s(){t(this.#t);const e=this.#t.preloadingAttempt;if("Prerender"!==e.action)return o.nothing;const r=C(e);return null===r?o.nothing:o.html`
        <${p.ReportView.ReportKey.litTagName}>${j(q.detailsFailureReason)}</${p.ReportView.ReportKey.litTagName}>
        <${p.ReportView.ReportValue.litTagName}>
          ${r}
        </${p.ReportView.ReportValue.litTagName}>
    `}#d(e,t){const r=x(e,t);return o.html`
      <${p.ReportView.ReportKey.litTagName}>${j(q.detailsRuleSet)}</${p.ReportView.ReportKey.litTagName}>
      <${p.ReportView.ReportValue.litTagName}>
        <div class="text-ellipsis" title="">
          <button class="link" role="link"
            @click=${()=>{d.Revealer.reveal(new b.PreloadingForward.RuleSetView(e.id))}}
            title=${j(q.buttonClickToRevealRuleSet)}
            style=${o.Directives.styleMap({color:"var(--sys-color-primary)","text-decoration":"underline"})}
            jslog=${m.action("reveal-rule-set").track({click:!0})}
          >
            ${r}
          </button>
        </div>
      </${p.ReportView.ReportValue.litTagName}>
    `}}customElements.define("devtools-resources-preloading-details-report-view",K);var X=Object.freeze({__proto__:null,PreloadingDetailsReportView:K});const J=new CSSStyleSheet;J.replaceSync("#container{padding:6px 12px;border-bottom:1px solid var(--sys-color-divider)}#contents{margin-top:14px}#contents *{background:var(--color-background-elevation-dark-only)}#title{padding-bottom:12px;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;border-bottom:1px solid var(--sys-color-divider);color:var(--sys-color-token-subtle);grid-column-start:span 2;font-weight:bold}#contents .key{grid-column-start:span 2;padding:12px 0;font-weight:bold;margin-bottom:-1.2em}#contents .value{grid-column-start:span 2;padding:12px 0}#footer{padding-top:12px;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;color:var(--sys-color-token-subtle);grid-column-start:span 2;font-weight:bold}#footer .icon-link{vertical-align:sub;float:right}devtools-icon-dialog{vertical-align:sub}\n/*# sourceURL=preloadingDisabledInfobar.css */\n");const Q={infobarPreloadingIsDisabled:"Speculative loading is disabled",infobarPreloadingIsForceEnabled:"Speculative loading is force-enabled",titleReasonsPreventingPreloading:"Reasons preventing speculative loading",headerDisabledByPreference:"User settings or extensions",descriptionDisabledByPreference:"Speculative loading is disabled because of user settings or an extension. Go to {PH1} to update your preference. Go to {PH2} to disable any extension that blocks speculative loading.",preloadingPagesSettings:"Preload pages settings",extensionsSettings:"Extensions settings",headerDisabledByDataSaver:"Data Saver",descriptionDisabledByDataSaver:"Speculative loading is disabled because of the operating system's Data Saver mode.",headerDisabledByBatterySaver:"Battery Saver",descriptionDisabledByBatterySaver:"Speculative loading is disabled because of the operating system's Battery Saver mode.",headerDisabledByHoldbackPrefetchSpeculationRules:"Prefetch was disabled, but is force-enabled now",descriptionDisabledByHoldbackPrefetchSpeculationRules:"Prefetch is forced-enabled because DevTools is open. When DevTools is closed, prefetch will be disabled because this browser session is part of a holdback group used for performance comparisons.",headerDisabledByHoldbackPrerenderSpeculationRules:"Prerendering was disabled, but is force-enabled now",descriptionDisabledByHoldbackPrerenderSpeculationRules:"Prerendering is forced-enabled because DevTools is open. When DevTools is closed, prerendering will be disabled because this browser session is part of a holdback group used for performance comparisons.",footerLearnMore:"Learn more"},Y=e.i18n.registerUIStrings("panels/application/preloading/components/PreloadingDisabledInfobar.ts",Q),Z=e.i18n.getLocalizedString.bind(void 0,Y),ee=u.RenderCoordinator.RenderCoordinator.instance();class te extends n.LegacyWrapper.WrappableComponent{static litTagName=o.literal`devtools-resources-preloading-disabled-infobar`;#e=this.attachShadow({mode:"open"});#t={disabledByPreference:!1,disabledByDataSaver:!1,disabledByBatterySaver:!1,disabledByHoldbackPrefetchSpeculationRules:!1,disabledByHoldbackPrerenderSpeculationRules:!1};connectedCallback(){this.#e.adoptedStyleSheets=[J],this.#r()}set data(e){this.#t=e,this.#r()}async#r(){await ee.write("PreloadingDisabledInfobar render",(()=>{o.render(this.#l(),this.#e,{host:this})}))}#l(){const e=this.#t.disabledByHoldbackPrefetchSpeculationRules||this.#t.disabledByHoldbackPrerenderSpeculationRules;let t;if(this.#t.disabledByPreference||this.#t.disabledByDataSaver||this.#t.disabledByBatterySaver)t=Z(Q.infobarPreloadingIsDisabled);else{if(!e)return o.nothing;t=Z(Q.infobarPreloadingIsForceEnabled)}return o.html`
      <div id='container'>
        <span id='header'>
          ${t}
        </span>

        <${v.IconDialog.IconDialog.litTagName}
          .data=${{iconData:{iconName:"info",color:"var(--icon-default-hover)",width:"16px",height:"16px"},closeButton:!0,position:"auto",horizontalAlignment:"auto",closeOnESC:!0,closeOnScroll:!1}}
          jslog=${m.dialog("preloading-disabled").track({resize:!0,keydown:"Escape"})}
        >
          ${this.#c()}
        </${v.IconDialog.IconDialog.litTagName}>
      </div>
    `}#c(){const e="https://developer.chrome.com/blog/prerender-pages/",t=h.XLink.XLink.create(e,Z(Q.footerLearnMore),void 0,void 0,"learn-more"),r=h.Fragment.html`
      <x-link class="icon-link devtools-link" tabindex="0" href="${e}"></x-link>
    `,a=new S.Icon.Icon;return a.data={iconName:"open-externally",color:"var(--icon-default-hover)",width:"16px",height:"16px"},r.append(a),o.html`
      <div id='contents'>
        <div id='title'>${Z(Q.titleReasonsPreventingPreloading)}</div>

        <${p.ReportView.Report.litTagName}>
          ${this.#u()}
          ${this.#p()}
          ${this.#g()}
          ${this.#h()}
          ${this.#m()}

          <${p.ReportView.ReportSectionDivider.litTagName}>
          </${p.ReportView.ReportSectionDivider.litTagName}>
        </${p.ReportView.Report.litTagName}>

        <div id='footer'>
          ${t}
          ${r}
        </div>
      </div>
    `}#b(e,t,r){return e?o.html`
      <div class='key'>
        ${t}
      </div>
      <div class='value'>
        ${r}
      </div>
    `:o.nothing}#u(){const t=new f.ChromeLink.ChromeLink;t.href="chrome://settings/performance",t.textContent=Z(Q.preloadingPagesSettings);const r=new f.ChromeLink.ChromeLink;r.href="chrome://extensions",r.textContent=Z(Q.extensionsSettings);const a=e.i18n.getFormatLocalizedString(Y,Q.descriptionDisabledByPreference,{PH1:t,PH2:r});return this.#b(this.#t.disabledByPreference,Z(Q.headerDisabledByPreference),a)}#p(){return this.#b(this.#t.disabledByDataSaver,Z(Q.headerDisabledByDataSaver),Z(Q.descriptionDisabledByDataSaver))}#g(){return this.#b(this.#t.disabledByBatterySaver,Z(Q.headerDisabledByBatterySaver),Z(Q.descriptionDisabledByBatterySaver))}#h(){return this.#b(this.#t.disabledByHoldbackPrefetchSpeculationRules,Z(Q.headerDisabledByHoldbackPrefetchSpeculationRules),Z(Q.descriptionDisabledByHoldbackPrefetchSpeculationRules))}#m(){return this.#b(this.#t.disabledByHoldbackPrerenderSpeculationRules,Z(Q.headerDisabledByHoldbackPrerenderSpeculationRules),Z(Q.descriptionDisabledByHoldbackPrerenderSpeculationRules))}}customElements.define("devtools-resources-preloading-disabled-infobar",te);var re=Object.freeze({__proto__:null,PreloadingDisabledInfobar:te});const ae=new CSSStyleSheet;ae.replaceSync(":host{overflow:auto;height:100%}.preloading-container{height:100%;display:flex;flex-direction:column}.preloading-header{font-size:15px;background-color:var(--sys-color-cdt-base-container);padding:1px 4px}.preloading-placeholder{flex-grow:1;display:flex;align-items:center;justify-content:center;font-size:13px;color:var(--sys-color-token-subtle)}devtools-data-grid-controller{border:1px solid var(--sys-color-divider)}.inline-icon{vertical-align:text-bottom}\n/*# sourceURL=preloadingGrid.css */\n");const ie={action:"Action",ruleSet:"Rule set",status:"Status"},ne=e.i18n.registerUIStrings("panels/application/preloading/components/PreloadingGrid.ts",ie),oe=e.i18n.getLocalizedString.bind(void 0,ne),{render:se,html:de}=o;class le extends n.LegacyWrapper.WrappableComponent{static litTagName=o.literal`devtools-resources-preloading-grid`;#e=this.attachShadow({mode:"open"});#t=null;connectedCallback(){this.#e.adoptedStyleSheets=[ae],this.#r()}update(e){this.#t=e,this.#r()}#r(){if(null===this.#t)return;const t={columns:[{id:"url",title:e.i18n.lockedString("URL"),widthWeighting:40,hideable:!1,visible:!0,sortable:!0},{id:"action",title:oe(ie.action),widthWeighting:15,hideable:!1,visible:!0,sortable:!0},{id:"rule-set",title:oe(ie.ruleSet),widthWeighting:20,hideable:!1,visible:!0,sortable:!0},{id:"status",title:oe(ie.status),widthWeighting:40,hideable:!1,visible:!0,sortable:!0}],rows:this.#a(),striped:!0};se(de`
      <div class="preloading-container">
        <${i.DataGridController.DataGridController.litTagName} .data=${t}>
        </${i.DataGridController.DataGridController.litTagName}>
      </div>
    `,this.#e,{host:this})}#a(){t(this.#t);const e=this.#t.pageURL,r=""===e?null:new d.ParsedURL.ParsedURL(e).securityOrigin();return this.#t.rows.map((t=>({cells:[{columnId:"id",value:t.id},{columnId:"url",value:this.#f(t,r),title:t.attempt.key.url},{columnId:"action",value:E(t.attempt.action)},{columnId:"rule-set",value:0===t.ruleSets.length?"":x(t.ruleSets[0],e)},{columnId:"status",value:D(t.attempt),renderer:e=>function(e,t){return"Failure"!==t?o.html`<div>${e}</div>`:o.html`
        <div
          style=${o.Directives.styleMap({color:"var(--sys-color-error)"})}
        >
          <${S.Icon.Icon.litTagName}
            .data=${{iconName:"cross-circle-filled",color:"var(--sys-color-error)",width:"16px",height:"16px"}}
            style=${o.Directives.styleMap({"vertical-align":"sub"})}
          >
          </${S.Icon.Icon.litTagName}>
          ${e}
        </div>
      `}(e,t.attempt.status)}]})))}#f(e,t){const r=e.attempt.key.url;return t&&r.startsWith(t)?r.slice(t.length):r}}customElements.define("devtools-resources-preloading-grid",le);var ce=Object.freeze({__proto__:null,i18nString:oe,PreloadingGrid:le});const ue={headerName:"Header name",initialNavigationValue:"Value in initial navigation",activationNavigationValue:"Value in activation navigation",missing:"(missing)"},pe=e.i18n.registerUIStrings("panels/application/preloading/components/PreloadingMismatchedHeadersGrid.ts",ue),ge=e.i18n.getLocalizedString.bind(void 0,pe),{render:he,html:me}=o;class be extends n.LegacyWrapper.WrappableComponent{static litTagName=o.literal`devtools-resources-preloading-mismatched-headers-grid`;#e=this.attachShadow({mode:"open"});#t=null;connectedCallback(){this.#e.adoptedStyleSheets=[ae],this.#r()}set data(e){null!==e.mismatchedHeaders&&(this.#t=e,this.#r())}#r(){if(null===this.#t)return;const e={columns:[{id:"header-name",title:ge(ue.headerName),widthWeighting:30,hideable:!1,visible:!0,sortable:!0},{id:"initial-value",title:ge(ue.initialNavigationValue),widthWeighting:30,hideable:!1,visible:!0,sortable:!0},{id:"activation-value",title:ge(ue.activationNavigationValue),widthWeighting:30,hideable:!1,visible:!0,sortable:!0}],rows:this.#a(),striped:!0};he(me`
        <div class="preloading-container">
          <${i.DataGridController.DataGridController.litTagName} .data=${e}>
          </${i.DataGridController.DataGridController.litTagName}>
        </div>
      `,this.#e,{host:this})}#a(){return t(this.#t),t(this.#t.mismatchedHeaders),this.#t.mismatchedHeaders.map((e=>({cells:[{columnId:"header-name",value:e.headerName},{columnId:"initial-value",value:e.initialValue??ge(ue.missing)},{columnId:"activation-value",value:e.activationValue??ge(ue.missing)}]})))}}customElements.define("devtools-resources-preloading-mismatched-headers-grid",be);var fe=Object.freeze({__proto__:null,i18nString:ge,PreloadingMismatchedHeadersGrid:be});const ve=new CSSStyleSheet;ve.replaceSync(".ruleset-header{padding:4px 8px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;border-bottom:1px solid var(--sys-color-divider)}.ruleset-header devtools-icon{vertical-align:sub}\n/*# sourceURL=RuleSetDetailsView.css */\n");const Se=u.RenderCoordinator.RenderCoordinator.instance(),Re=await w.CodeHighlighter.languageFromMIME("application/json");class we extends n.LegacyWrapper.WrappableComponent{#e=this.attachShadow({mode:"open"});#t=null;#v;connectedCallback(){this.#e.adoptedStyleSheets=[ve]}set data(e){this.#t=e,this.#r()}async#r(){await Se.write("RuleSetDetailsView render",(()=>{null!==this.#t?o.render(o.html`
        <div class="content">
          <div class="ruleset-header" id="ruleset-url">${this.#t?.url||r.TargetManager.TargetManager.instance().inspectedURL()}</div>
          ${this.#S()}
        </div>
        <div class="text-ellipsis">
          ${this.#R()}
        </div>
      `,this.#e,{host:this}):o.render(o.nothing,this.#e,{host:this})}))}#S(){return t(this.#t),void 0===this.#t.errorMessage?o.nothing:o.html`
      <div class="ruleset-header">
        <${S.Icon.Icon.litTagName}
          .data=${{iconName:"cross-circle",color:"var(--icon-error)",width:"16px",height:"16px"}}>
        </${S.Icon.Icon.litTagName}>
        <span id="error-message-text">${this.#t.errorMessage}</span>
      </div>
    `}#R(){return this.#v=R.EditorState.create({doc:this.#t?.sourceText,extensions:[P.Config.baseConfiguration(this.#t?.sourceText||""),R.lineNumbers(),R.EditorState.readOnly.of(!0),Re,R.syntaxHighlighting(w.CodeHighlighter.highlightStyle)]}),o.html`
        <${P.TextEditor.TextEditor.litTagName} .style.flexGrow = '1' .state=${this.#v}></${P.TextEditor.TextEditor.litTagName}>
      `}}customElements.define("devtools-resources-rulesets-details-view",we);var Pe=Object.freeze({__proto__:null,RuleSetDetailsView:we});const ye=new CSSStyleSheet;ye.replaceSync(":host{overflow:auto;height:100%}.ruleset-container{height:100%;display:flex;flex-direction:column}devtools-data-grid-controller{border:1px solid var(--sys-color-divider)}.inline-icon{vertical-align:text-bottom}\n/*# sourceURL=ruleSetGrid.css */\n");const Ne={ruleSet:"Rule set",status:"Status",buttonClickToRevealInElementsPanel:"Click to reveal in Elements panel",buttonClickToRevealInNetworkPanel:"Click to reveal in Network panel",errors:"{errorCount, plural, =1 {# error} other {# errors}}",buttonRevealPreloadsAssociatedWithRuleSet:"Reveal speculative loads associated with this rule set"},Te=e.i18n.registerUIStrings("panels/application/preloading/components/RuleSetGrid.ts",Ne),Fe=e.i18n.getLocalizedString.bind(void 0,Te);class ke extends n.LegacyWrapper.WrappableComponent{static litTagName=o.literal`devtools-resources-ruleset-grid`;#e=this.attachShadow({mode:"open"});#t=null;connectedCallback(){this.#e.adoptedStyleSheets=[ye],this.#r()}update(e){this.#t=e,this.#r()}#r(){if(null===this.#t)return;const e={columns:[{id:"rule-set",title:Fe(Ne.ruleSet),widthWeighting:20,hideable:!1,visible:!0,sortable:!0},{id:"status",title:Fe(Ne.status),widthWeighting:80,hideable:!1,visible:!0,sortable:!0}],rows:this.#a(),striped:!0};o.render(o.html`
      <div class="ruleset-container"
      jslog=${m.pane("preloading-rules")}>
        <${i.DataGridController.DataGridController.litTagName} .data=${e}>
        </${i.DataGridController.DataGridController.litTagName}>
      </div>
    `,this.#e,{host:this})}#a(){t(this.#t);const e=this.#t.pageURL;return this.#t.rows.map((a=>({cells:[{columnId:"id",value:a.ruleSet.id},{columnId:"rule-set",value:"",renderer:()=>function(e,a){function i(e,a){t(e.backendNodeId);const i=async()=>{t(e.backendNodeId);const a=r.TargetManager.TargetManager.instance().scopeTarget();null!==a&&await d.Revealer.reveal(new r.DOMModel.DeferredDOMNode(a,e.backendNodeId))};return o.html`
      <button class="link" role="link"
        @click=${i}
        title=${Fe(Ne.buttonClickToRevealInElementsPanel)}
        style=${o.Directives.styleMap({border:"none",background:"none",color:"var(--icon-link)",cursor:"pointer","text-decoration":"underline","padding-inline-start":"0","padding-inline-end":"0"})}
        jslog=${m.action("reveal-in-elements").track({click:!0})}
      >
        <${S.Icon.Icon.litTagName}
          .data=${{iconName:"code-circle",color:"var(--icon-link)",width:"16px",height:"16px"}}
          style=${o.Directives.styleMap({"vertical-align":"sub"})}
        >
        </${S.Icon.Icon.litTagName}>
        ${a}
      </button>
    `}function n(e,a){t(e.url),t(e.requestId);const i=async()=>{t(e.requestId);const a=r.TargetManager.TargetManager.instance().scopeTarget()?.model(r.NetworkManager.NetworkManager)?.requestForId(e.requestId)||null;if(null===a)return;const i=y.UIRequestLocation.UIRequestLocation.tab(a,"preview",{clearFilter:!1});await d.Revealer.reveal(i)};return o.html`
      <button class="link" role="link"
        @click=${i}
        title=${Fe(Ne.buttonClickToRevealInNetworkPanel)}
        style=${o.Directives.styleMap({border:"none",background:"none",color:"var(--icon-link)",cursor:"pointer","text-decoration":"underline","padding-inline-start":"0","padding-inline-end":"0"})}
      >
        <${S.Icon.Icon.litTagName}
         .data=${{iconName:"arrow-up-down-circle",color:"var(--icon-link)",width:"16px",height:"16px"}}
          style=${o.Directives.styleMap({"vertical-align":"sub"})}
        >
        </${S.Icon.Icon.litTagName}>
        ${a}
      </button>
    `}const s=x(e,a);if(void 0!==e.backendNodeId)return i(e,s);if(void 0!==e.url&&e.requestId)return n(e,s);return o.html`${s}`}(a.ruleSet,e)},{columnId:"status",value:a.preloadsStatusSummary,renderer:e=>function(e,t){function r(e,t){const r=async()=>{await d.Revealer.reveal(new b.PreloadingForward.AttemptViewWithFilter(t.id))};return o.html`
      <button class="link" role="link"
        @click=${r}
        title=${Fe(Ne.buttonRevealPreloadsAssociatedWithRuleSet)}
        style=${o.Directives.styleMap({color:"var(--sys-color-primary)","text-decoration":"underline",cursor:"pointer",border:"none",background:"none","padding-inline-start":"0","padding-inline-end":"0"})}
        jslog=${m.action("reveal-preloads").track({click:!0})}>
        ${e}
      </button>
    `}function a(){const e=Fe(Ne.errors,{errorCount:1});return o.html`
      <span
        style=${o.Directives.styleMap({color:"var(--sys-color-error)"})}
      >
        ${e}
      </span>
    `}switch(t.errorType){case void 0:return r(e,t);case"SourceIsNotJsonObject":return a();case"InvalidRulesSkipped":return o.html`${a()} ${r(e,t)}`}}(e,a.ruleSet)}]})))}}customElements.define("devtools-resources-ruleset-grid",ke);var $e=Object.freeze({__proto__:null,i18nString:Fe,RuleSetGrid:ke});const Ie=new CSSStyleSheet;Ie.replaceSync(":host{overflow:auto;height:100%}button{font-size:inherit}devtools-report{padding:1em 0}devtools-report-section-header{padding-bottom:0;margin-bottom:-1.5em}devtools-report-section{min-width:fit-content}devtools-report-divider{margin:1em 0}.reveal-links{white-space:nowrap}.link{border:none;background:none;color:var(--sys-color-primary);text-decoration:underline;cursor:pointer;outline-offset:2px;padding:0}.status-badge-container{white-space:nowrap;margin:8px 0 24px}.status-badge-container span{margin-right:2px}.status-badge{border-radius:4px;padding:4px;devtools-icon{width:16px;height:16px}}.status-badge-success{background:var(--sys-color-tertiary-container)}.status-badge-failure{background:var(--sys-color-error-container)}.status-badge-neutral{background:var(--sys-color-neutral-container)}\n/*# sourceURL=usedPreloadingView.css */\n");const Ce={speculativeLoadingStatusForThisPage:"Speculative loading status for this page",detailsFailureReason:"Failure reason",downgradedPrefetchUsed:"The initiating page attempted to prerender this page's URL. The prerender failed, but the resulting response body was still used as a prefetch.",prefetchUsed:"This page was successfully prefetched.",prerenderUsed:"This page was successfully prerendered.",prefetchFailed:"The initiating page attempted to prefetch this page's URL, but the prefetch failed, so a full navigation was performed instead.",prerenderFailed:"The initiating page attempted to prerender this page's URL, but the prerender failed, so a full navigation was performed instead.",noPreloads:"The initiating page did not attempt to speculatively load this page's URL.",currentURL:"Current URL",preloadedURLs:"URLs being speculatively loaded by the initiating page",speculationsInitiatedByThisPage:"Speculations initiated by this page",viewAllRules:"View all speculation rules",viewAllSpeculations:"View all speculations",learnMore:"Learn more: Speculative loading on developer.chrome.com",mismatchedHeadersDetail:"Mismatched HTTP request headers",badgeSuccess:"Success",badgeFailure:"Failure",badgeNoSpeculativeLoads:"No speculative loads",badgeNotTriggeredWithCount:"{n, plural, =1 {# not triggered} other {# not triggered}}",badgeInProgressWithCount:"{n, plural, =1 {# in progress} other {# in progress}}",badgeSuccessWithCount:"{n, plural, =1 {# success} other {# success}}",badgeFailureWithCount:"{n, plural, =1 {# failure} other {# failures}}"},xe=e.i18n.registerUIStrings("panels/application/preloading/components/UsedPreloadingView.ts",Ce),Ee=e.i18n.getLocalizedString.bind(void 0,xe),De=u.RenderCoordinator.RenderCoordinator.instance();class Ue extends n.LegacyWrapper.WrappableComponent{static litTagName=o.literal`devtools-resources-used-preloading-view`;#e=this.attachShadow({mode:"open"});#t={pageURL:"",previousAttempts:[],currentAttempts:[]};connectedCallback(){this.#e.adoptedStyleSheets=[Ie]}set data(e){this.#t=e,this.#r()}async#r(){await De.write("UsedPreloadingView render",(()=>{o.render(this.#l(),this.#e,{host:this})}))}#l(){return o.html`
      <${p.ReportView.Report.litTagName}>
        ${this.#w()}

        <${p.ReportView.ReportSectionDivider.litTagName}></${p.ReportView.ReportSectionDivider.litTagName}>

        ${this.#P()}

        <${p.ReportView.ReportSectionDivider.litTagName}></${p.ReportView.ReportSectionDivider.litTagName}>

        <${p.ReportView.ReportSection.litTagName}>
          ${h.XLink.XLink.create("https://developer.chrome.com/blog/prerender-pages/",Ee(Ce.learnMore),"link",void 0,"learn-more")}
        </${p.ReportView.ReportSection.litTagName}>
      </${p.ReportView.Report.litTagName}>
    `}#w(){const e=d.ParsedURL.ParsedURL.urlWithoutHash(this.#t.pageURL),r=this.#t.previousAttempts.filter((t=>d.ParsedURL.ParsedURL.urlWithoutHash(t.key.url)===e)),a=r.filter((e=>"Prefetch"===e.key.action))[0],i=r.filter((e=>"Prerender"===e.key.action))[0];let n,s,l,c="NoPreloads";switch(c="Failure"===i?.status&&"Success"===a?.status?"DowngradedPrerenderToPrefetchAndUsed":"Success"===a?.status?"PrefetchUsed":"Success"===i?.status?"PrerenderUsed":"Failure"===a?.status?"PrefetchFailed":"Failure"===i?.status?"PrerenderFailed":"NoPreloads",c){case"DowngradedPrerenderToPrefetchAndUsed":n=this.#y(),s=o.html`${Ee(Ce.downgradedPrefetchUsed)}`;break;case"PrefetchUsed":n=this.#y(),s=o.html`${Ee(Ce.prefetchUsed)}`;break;case"PrerenderUsed":n=this.#y(),s=o.html`${Ee(Ce.prerenderUsed)}`;break;case"PrefetchFailed":n=this.#N(),s=o.html`${Ee(Ce.prefetchFailed)}`;break;case"PrerenderFailed":n=this.#N(),s=o.html`${Ee(Ce.prerenderFailed)}`;break;case"NoPreloads":n=this.#T(Ee(Ce.badgeNoSpeculativeLoads)),s=o.html`${Ee(Ce.noPreloads)}`}"PrefetchFailed"===c?(t(a),l=I(a)):"PrerenderFailed"!==c&&"DowngradedPrerenderToPrefetchAndUsed"!==c||(t(i),l=C(i));let u=o.nothing;return void 0!==l&&(u=o.html`
      <${p.ReportView.ReportSectionHeader.litTagName}>${Ee(Ce.detailsFailureReason)}</${p.ReportView.ReportSectionHeader.litTagName}>
      <${p.ReportView.ReportSection.litTagName}>
        ${l}
      </${p.ReportView.ReportSection.litTagName}>
      `),o.html`
      <${p.ReportView.ReportSectionHeader.litTagName}>${Ee(Ce.speculativeLoadingStatusForThisPage)}</${p.ReportView.ReportSectionHeader.litTagName}>
      <${p.ReportView.ReportSection.litTagName}>
        <div>
          <div class="status-badge-container">
            ${n}
          </div>
          <div>
            ${s}
          </div>
        </div>
      </${p.ReportView.ReportSection.litTagName}>

      ${u}

      ${this.#F(c)}
      ${this.#k()}
    `}#F(e){if("NoPreloads"!==e||0===this.#t.previousAttempts.length)return o.nothing;const t=this.#t.previousAttempts.map((e=>({url:e.key.url,action:e.key.action,status:e.status}))),r={pageURL:this.#t.pageURL,rows:t};return o.html`
      <${p.ReportView.ReportSectionHeader.litTagName}>${Ee(Ce.currentURL)}</${p.ReportView.ReportSectionHeader.litTagName}>
      <${p.ReportView.ReportSection.litTagName}>
        ${h.XLink.XLink.create(this.#t.pageURL,void 0,"link",void 0,"current-url")}
      </${p.ReportView.ReportSection.litTagName}>

      <${p.ReportView.ReportSectionHeader.litTagName}>${Ee(Ce.preloadedURLs)}</${p.ReportView.ReportSectionHeader.litTagName}>
      <${p.ReportView.ReportSection.litTagName}
      jslog=${m.section("preloaded-urls")}>
        <${A.litTagName}
          .data=${r}></${A.litTagName}>
      </${p.ReportView.ReportSection.litTagName}>
    `}#k(){const e=this.#t.previousAttempts.find((e=>"Prerender"===e.action&&null!==e.mismatchedHeaders));if(void 0===e)return o.nothing;if(e.key.url!==this.#t.pageURL)throw new Error("unreachable");return o.html`
      <${p.ReportView.ReportSectionHeader.litTagName}>${Ee(Ce.mismatchedHeadersDetail)}</${p.ReportView.ReportSectionHeader.litTagName}>
      <${p.ReportView.ReportSection.litTagName}>
        <${be.litTagName}
          .data=${e}></${be.litTagName}>
      </${p.ReportView.ReportSection.litTagName}>
    `}#P(){const e=this.#t.currentAttempts.reduce(((e,t)=>(e.set(t.status,(e.get(t.status)??0)+1),e)),new Map),t=e.get("NotTriggered")??0,r=e.get("Ready")??0,a=e.get("Failure")??0,i=(e.get("Pending")??0)+(e.get("Running")??0),n=[];0===this.#t.currentAttempts.length&&n.push(this.#T(Ee(Ce.badgeNoSpeculativeLoads))),t>0&&n.push(this.#T(Ee(Ce.badgeNotTriggeredWithCount,{n:t}))),i>0&&n.push(this.#T(Ee(Ce.badgeInProgressWithCount,{n:i}))),r>0&&n.push(this.#y(r)),a>0&&n.push(this.#N(a));return o.html`
      <${p.ReportView.ReportSectionHeader.litTagName}>${Ee(Ce.speculationsInitiatedByThisPage)}</${p.ReportView.ReportSectionHeader.litTagName}>
      <${p.ReportView.ReportSection.litTagName}>
        <div>
          <div class="status-badge-container">
            ${n}
          </div>

          <div class="reveal-links">
            <button class="link devtools-link" @click=${()=>{d.Revealer.reveal(new b.PreloadingForward.RuleSetView(null))}}
            jslog=${m.action("view-all-rules").track({click:!0})}>
              ${Ee(Ce.viewAllRules)}
            </button>
           ・
            <button class="link devtools-link" @click=${()=>{d.Revealer.reveal(new b.PreloadingForward.AttemptViewWithFilter(null))}}
            jslog=${m.action("view-all-speculations").track({click:!0})}>
             ${Ee(Ce.viewAllSpeculations)}
            </button>
          </div>
        </div>
      </${p.ReportView.ReportSection.litTagName}>
    `}#y(e){let t;return t=void 0===e?Ee(Ce.badgeSuccess):Ee(Ce.badgeSuccessWithCount,{n:e}),this.#$("status-badge status-badge-success","check-circle",t)}#N(e){let t;return t=void 0===e?Ee(Ce.badgeFailure):Ee(Ce.badgeFailureWithCount,{n:e}),this.#$("status-badge status-badge-failure","cross-circle",t)}#T(e){return this.#$("status-badge status-badge-neutral","clear",e)}#$(e,t,r){return o.html`
      <span class=${e}>
        <${S.Icon.Icon.litTagName} name=${t}></${S.Icon.Icon.litTagName}>
        <span>
          ${r}
        </span>
      </span>
    `}}customElements.define("devtools-resources-used-preloading-view",Ue);var Le=Object.freeze({__proto__:null,UsedPreloadingView:Ue});export{O as MismatchedPreloadingGrid,X as PreloadingDetailsReportView,re as PreloadingDisabledInfobar,ce as PreloadingGrid,fe as PreloadingMismatchedHeadersGrid,Pe as RuleSetDetailsView,$e as RuleSetGrid,Le as UsedPreloadingView};
