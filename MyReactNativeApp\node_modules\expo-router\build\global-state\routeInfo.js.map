{"version": 3, "file": "routeInfo.js", "sourceRoot": "", "sources": ["../../src/global-state/routeInfo.ts"], "names": [], "mappings": ";;;AA+CA,sDA8IC;AA1LD,4CAAkD;AAClD,2EAA+D;AAYlD,QAAA,gBAAgB,GAAc;IACzC,mBAAmB,EAAE,EAAE;IACvB,YAAY,EAAE,IAAI,eAAe,EAAE;IACnC,QAAQ,EAAE,GAAG;IACb,MAAM,EAAE,EAAE;IACV,QAAQ,EAAE,EAAE;IACZ,kBAAkB,EAAE,GAAG;IACvB,6CAA6C;IAC7C,OAAO,EAAE,KAAK;CACf,CAAC;AAsBF,SAAgB,qBAAqB,CAAC,KAAmB;IACvD,IAAI,CAAC,KAAK;QAAE,OAAO,wBAAgB,CAAC;IAEpC,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAI,KAAK,CAAC,IAAI,KAAK,8BAAkB,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,kCAAkC,8BAAkB,aAAa,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACjG,CAAC;IAED,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAEpB,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,MAAM,MAAM,GAAwB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAExD,OAAO,KAAK,EAAE,CAAC;QACb,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAEpC,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAC3B,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACvC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,IAAI,WAAW,GAAyC,KAAK,CAAC,MAAM,CAAC;IACrE,OAAO,WAAW,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;QAC9C,IAAI,OAAO,WAAW,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;gBAC/C,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,OAAO,WAAW,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;YACjF,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,SAAS,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;QACjF,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;YAChD,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QACxB,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;QAC9C,QAAQ,CAAC,GAAG,EAAE,CAAC;IACjB,CAAC;IAED,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IACxB,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAExB,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;IAErC,MAAM,QAAQ,GACZ,GAAG;QACH,QAAQ;aACL,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YAClB,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC;aACD,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACnB,IAAI,OAAO,KAAK,YAAY,EAAE,CAAC;gBAC7B,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;gBAEzC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAE5B,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;oBACxC,yDAAyD;oBACzD,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;oBACvC,OAAO,YAAY,CAAC;gBACtB,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,YAAY,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/D,IAAI,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAErC,8CAA8C;gBAC9C,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC5B,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;gBACjC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAE1B,+BAA+B;gBAC/B,OAAO,MAAM,IAAI,EAAE,CAAC;YACtB,CAAC;iBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5D,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;gBAChC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAE1B,+BAA+B;gBAC/B,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,OAAO,CAAC,CAAC;YACnB,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAC;IAEf,MAAM,YAAY,GAAG,IAAI,eAAe,CACtC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC9C,+CAA+C;QAC/C,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,CAAC;QACZ,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CACH,CAAC;IAEF,IAAI,IAAwB,CAAC;IAC7B,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC;QAC1C,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,0FAA0F;IAC1F,MAAM,iBAAiB,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;IAClD,IAAI,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC3F,kBAAkB,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC;IAEjF,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,mBAAmB,EAAE,IAAA,sCAAa,EAAC,kBAAkB,CAAC;QACtD,YAAY;QACZ,kBAAkB;QAClB,6CAA6C;QAC7C,OAAO,EAAE,KAAK;KACf,CAAC;AACJ,CAAC", "sourcesContent": ["import type { NavigationState, PartialState } from '@react-navigation/native';\n\nimport type { FocusedRouteState } from './router-store';\nimport { INTERNAL_SLOT_NAME } from '../constants';\nimport { appendBaseUrl } from '../fork/getPathFromState-forks';\n\nexport type UrlObject = {\n  unstable_globalHref: string;\n  pathname: string;\n  readonly params: Record<string, string | string[]>;\n  searchParams: URLSearchParams;\n  segments: string[];\n  pathnameWithParams: string;\n  isIndex: boolean;\n};\n\nexport const defaultRouteInfo: UrlObject = {\n  unstable_globalHref: '',\n  searchParams: new URLSearchParams(),\n  pathname: '/',\n  params: {},\n  segments: [],\n  pathnameWithParams: '/',\n  // TODO: Remove this, it is not used anywhere\n  isIndex: false,\n};\n\n/**\n * A better typed version of `FocusedRouteState` that is easier to parse\n */\ntype StrictState = (FocusedRouteState | NavigationState | PartialState<NavigationState>) & {\n  routes: {\n    key?: string;\n    name: string;\n    params?: StrictFocusedRouteParams;\n    path?: string;\n    state?: StrictState;\n  }[];\n};\n\ntype StrictFocusedRouteParams =\n  | Record<string, string | string[]>\n  | {\n      screen?: string;\n      params?: StrictFocusedRouteParams;\n    };\n\nexport function getRouteInfoFromState(state?: StrictState): UrlObject {\n  if (!state) return defaultRouteInfo;\n\n  let route = state.routes[0];\n  if (route.name !== INTERNAL_SLOT_NAME) {\n    throw new Error(`Expected the first route to be ${INTERNAL_SLOT_NAME}, but got ${route.name}`);\n  }\n\n  state = route.state;\n\n  const segments: string[] = [];\n  const params: UrlObject['params'] = Object.create(null);\n\n  while (state) {\n    route = state.routes['index' in state && state.index ? state.index : 0];\n\n    Object.assign(params, route.params);\n\n    let routeName = route.name;\n    if (routeName.startsWith('/')) {\n      routeName = routeName.slice(1);\n    }\n\n    segments.push(...routeName.split('/'));\n    state = route.state;\n  }\n\n  /**\n   * If React Navigation didn't render the entire tree (e.g it was interrupted in a layout)\n   * then the state maybe incomplete. The reset of the path is in the params, instead of being a route\n   */\n  let routeParams: StrictFocusedRouteParams | undefined = route.params;\n  while (routeParams && 'screen' in routeParams) {\n    if (typeof routeParams.screen === 'string') {\n      const screen = routeParams.screen.startsWith('/')\n        ? routeParams.screen.slice(1)\n        : routeParams.screen;\n      segments.push(...screen.split('/'));\n    }\n\n    if (typeof routeParams.params === 'object' && !Array.isArray(routeParams.params)) {\n      routeParams = routeParams.params;\n    } else {\n      routeParams = undefined;\n    }\n  }\n\n  if (route.params && 'screen' in route.params && route.params.screen === 'string') {\n    const screen = route.params.screen.startsWith('/')\n      ? route.params.screen.slice(1)\n      : route.params.screen;\n    segments.push(...screen.split('/'));\n  }\n\n  if (segments[segments.length - 1] === 'index') {\n    segments.pop();\n  }\n\n  delete params['screen'];\n  delete params['params'];\n\n  const pathParams = new Set<string>();\n\n  const pathname =\n    '/' +\n    segments\n      .filter((segment) => {\n        return !(segment.startsWith('(') && segment.endsWith(')'));\n      })\n      .flatMap((segment) => {\n        if (segment === '+not-found') {\n          const notFoundPath = params['not-found'];\n\n          pathParams.add('not-found');\n\n          if (typeof notFoundPath === 'undefined') {\n            // Not founds are optional, do nothing if its not present\n            return [];\n          } else if (Array.isArray(notFoundPath)) {\n            return notFoundPath;\n          } else {\n            return [notFoundPath];\n          }\n        } else if (segment.startsWith('[...') && segment.endsWith(']')) {\n          let paramName = segment.slice(4, -1);\n\n          // Legacy for React Navigation optional params\n          if (paramName.endsWith('?')) {\n            paramName = paramName.slice(0, -1);\n          }\n\n          const values = params[paramName];\n          pathParams.add(paramName);\n\n          // Catchall params are optional\n          return values || [];\n        } else if (segment.startsWith('[') && segment.endsWith(']')) {\n          const paramName = segment.slice(1, -1);\n          const value = params[paramName];\n          pathParams.add(paramName);\n\n          // Optional params are optional\n          return value ? [value] : [];\n        } else {\n          return [segment];\n        }\n      })\n      .join('/');\n\n  const searchParams = new URLSearchParams(\n    Object.entries(params).flatMap(([key, value]) => {\n      // Search params should not include path params\n      if (pathParams.has(key)) {\n        return [];\n      } else if (Array.isArray(value)) {\n        return value.map((v) => [key, v]);\n      }\n      return [[key, value]];\n    })\n  );\n\n  let hash: string | undefined;\n  if (searchParams.has('#')) {\n    hash = searchParams.get('#') || undefined;\n    searchParams.delete('#');\n  }\n\n  // We cannot use searchParams.size because it is not included in the React Native polyfill\n  const searchParamString = searchParams.toString();\n  let pathnameWithParams = searchParamString ? pathname + '?' + searchParamString : pathname;\n  pathnameWithParams = hash ? pathnameWithParams + '#' + hash : pathnameWithParams;\n\n  return {\n    segments,\n    pathname,\n    params,\n    unstable_globalHref: appendBaseUrl(pathnameWithParams),\n    searchParams,\n    pathnameWithParams,\n    // TODO: Remove this, it is not used anywhere\n    isIndex: false,\n  };\n}\n"]}