{"version": 3, "file": "StackClient.js", "sourceRoot": "", "sources": ["../../src/layouts/StackClient.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AACb,qDAWkC;AAClC,iEAIwC;AAGxC,2DAAwD;AACxD,8CAA+D;AAC/D,kDAA+C;AAI/C,MAAM,oBAAoB,GAAG,IAAA,yCAA0B,GAAE,CAAC,SAAS,CAAC;AAEpE,MAAM,OAAO,GAAG,IAAA,qCAAiB,EAK/B,oBAAoB,CAAC,CAAC;AAExB,SAAS,aAAa,CACpB,MAAwB;IAExB,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,MAAM;QACtB,MAAM,CAAC,IAAI,KAAK,UAAU;QAC1B,MAAM,CAAC,IAAI,KAAK,KAAK;QACrB,MAAM,CAAC,IAAI,KAAK,YAAY;QAC5B,MAAM,CAAC,IAAI,KAAK,SAAS,CAC1B,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACI,MAAM,mBAAmB,GAAmE,CACjG,QAAQ,EACR,EAAE;IACF,OAAO;QACL,iBAAiB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YAC5C,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,OAAO,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAC5D,CAAC;YAED,oFAAoF;YACpF,MAAM,qBAAqB,GACzB,MAAM,CAAC,OAAO,IAAI,UAAU,IAAI,MAAM,CAAC,OAAO;gBAC5C,CAAC,CAAE,MAAM,CAAC,OAAO,CAAC,QAA4B;gBAC9C,CAAC,CAAC,SAAS,CAAC;YAEhB,0CAA0C;YAC1C,SAAS,aAAa,CAAC,EAAU;gBAC/B,+EAA+E;gBAC/E,IACE,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC;oBACtB,CAAC,MAAM,CAAC,OAAO;oBACf,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC;oBAC3B,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EACvC,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;gBAEjC,OAAO;gBACL,6FAA6F;gBAC7F,qBAAqB,CAAC,qBAAqB,EAAE,IAAI,CAAC;oBAClD,qFAAqF;oBACrF,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;oBAC5B,gFAAgF;oBAChF,EAAE,CACH,CAAC;YACJ,CAAC;YAED,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,MAAM,CAAC,CAAC,CAAC;oBACZ;;;;;uBAKG;oBACH,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE;wBAC1D,GAAG,OAAO;wBACV,cAAc,EAAE;4BACd,GAAG,OAAO,CAAC,cAAc;4BACzB,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE;yBACvC;qBACF,CAAC,CAAC;oBAEH;;;;uBAIG;oBACH,OAAO,qBAAqB;wBAC1B,CAAC,CAAC,cAAc,CAAC,SAAS,EAAE,qBAAqB,CAAC;wBAClD,CAAC,CAAC,SAAS,CAAC;gBAChB,CAAC;gBACD,KAAK,UAAU,CAAC,CAAC,CAAC;oBAChB;;;;;;;;;;uBAUG;oBACH,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE;wBAC1D,GAAG,OAAO;wBACV,cAAc,EAAE;4BACd,GAAG,OAAO,CAAC,cAAc;4BACzB,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC,CAAC,OAAO,EAAE,EAAE;gCAC/C,OAAO,IAAA,0BAAa,EAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BACrD,CAAC,CAAC;yBACH;qBACF,CAAC,CAAC;oBAEH;;;;uBAIG;oBACH,OAAO,qBAAqB;wBAC1B,CAAC,CAAC,cAAc,CAAC,SAAS,EAAE,qBAAqB,CAAC;wBAClD,CAAC,CAAC,SAAS,CAAC;gBAChB,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC;oBACR,OAAO,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAzGW,QAAA,mBAAmB,uBAyG9B;AAEF,SAAS,qBAAqB,CAC5B,WAAwC,EACxC,IAAY;IAEZ,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE,CAAC;QACtC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;IAC9D,CAAC;SAAM,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QAChC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,IAAA,0BAAa,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;GAGG;AACH,SAAS,cAAc,CAKrB,KAAQ,EAAE,QAAyB;IACnC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5D,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAE1B,MAAM,KAAK,GAAG,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAEpD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAE7C,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,OAAO,KAAK,CAAC;IACf,CAAC;IAED,iEAAiE;IACjE,IAAI,MAAM,GAAG,KAAK,CAAC,MAA2D,CAAC;IAC/E,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACtC,8CAA8C;QAC9C,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qDAAqD;QACrD,OAAO,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,GAAG,KAAK;QACR,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC;QACxB,MAAM;KACP,CAAC;AACJ,CAAC;AAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,CAAC,KAAqC,EAAE,EAAE;IACxC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,2BAAmB,CAAC,EAAG,CAAC;AACtE,CAAC,EACD;IACE,MAAM,EAAE,OAAO,CAAC,MAEP;IACT,SAAS,EAAT,qBAAS;CACV,CACF,CAAC;AAEF,kBAAe,KAAK,CAAC;AAEd,MAAM,WAAW,GAAyB,CAAC,OAAO,EAAE,EAAE;IAC3D,MAAM,MAAM,GAAG,IAAA,oBAAa,EAAC,OAAO,CAAC,CAAC;IACtC,OAAO;QACL,GAAG,MAAM;QACT,GAAG,IAAA,2BAAmB,EAAC,MAAM,CAAC;KAC/B,CAAC;AACJ,CAAC,CAAC;AANW,QAAA,WAAW,eAMtB", "sourcesContent": ["'use client';\nimport {\n  CommonNavigationAction,\n  NavigationAction,\n  ParamListBase,\n  PartialRoute,\n  PartialState,\n  Route,\n  RouterConfigOptions,\n  StackRouter as RNStackRouter,\n  StackActionType,\n  StackNavigationState,\n} from '@react-navigation/native';\nimport {\n  NativeStackNavigationEventMap,\n  NativeStackNavigationOptions,\n  createNativeStackNavigator,\n} from '@react-navigation/native-stack';\nimport { ComponentProps } from 'react';\n\nimport { withLayoutContext } from './withLayoutContext';\nimport { SingularOptions, getSingularId } from '../useScreens';\nimport { Protected } from '../views/Protected';\n\ntype GetId = NonNullable<RouterConfigOptions['routeGetIdList'][string]>;\n\nconst NativeStackNavigator = createNativeStackNavigator().Navigator;\n\nconst RNStack = withLayoutContext<\n  NativeStackNavigationOptions,\n  typeof NativeStackNavigator,\n  StackNavigationState<ParamListBase>,\n  NativeStackNavigationEventMap\n>(NativeStackNavigator);\n\nfunction isStackAction(\n  action: NavigationAction\n): action is StackActionType | Extract<CommonNavigationAction, { type: 'NAVIGATE' }> {\n  return (\n    action.type === 'PUSH' ||\n    action.type === 'NAVIGATE' ||\n    action.type === 'POP' ||\n    action.type === 'POP_TO_TOP' ||\n    action.type === 'REPLACE'\n  );\n}\n\n/**\n * React Navigation matches a screen by its name or a 'getID' function that uniquely identifies a screen.\n * When a screen has been uniquely identified, the Stack can only have one instance of that screen.\n *\n * Expo Router allows for a screen to be matched by name and path params, a 'getID' function or a singular id.\n *\n * Instead of reimplementing the entire StackRouter, we can override the getStateForAction method to handle the singular screen logic.\n *\n */\nexport const stackRouterOverride: NonNullable<ComponentProps<typeof RNStack>['UNSTABLE_router']> = (\n  original\n) => {\n  return {\n    getStateForAction: (state, action, options) => {\n      if (action.target && action.target !== state.key) {\n        return null;\n      }\n\n      if (!isStackAction(action)) {\n        return original.getStateForAction(state, action, options);\n      }\n\n      // The dynamic getId added to an action, `router.push('screen', { singular: true })`\n      const actionSingularOptions =\n        action.payload && 'singular' in action.payload\n          ? (action.payload.singular as SingularOptions)\n          : undefined;\n\n      // Handle if 'getID' or 'singular' is set.\n      function getIdFunction(fn?: GetId): GetId | undefined {\n        // Actions can be fired by the user, so we do need to validate their structure.\n        if (\n          !('payload' in action) ||\n          !action.payload ||\n          !('name' in action.payload) ||\n          typeof action.payload.name !== 'string'\n        ) {\n          return;\n        }\n\n        const name = action.payload.name;\n\n        return (\n          // The dynamic singular added to an action, `router.push('screen', { singular: () => 'id' })`\n          getActionSingularIdFn(actionSingularOptions, name) ||\n          // The static getId added as a prop to `<Screen singular />` or `<Screen getId={} />`\n          options.routeGetIdList[name] ||\n          // The custom singular added by Expo Router to support its concept of `navigate`\n          fn\n        );\n      }\n\n      switch (action.type) {\n        case 'PUSH': {\n          /**\n           * PUSH should always push\n           *\n           * If 'getID' or 'singular' is set and a match is found, instead of pushing a new screen,\n           * the existing screen will be moved to the HEAD of the stack. If there are multiple matches, the rest will be removed.\n           */\n          const nextState = original.getStateForAction(state, action, {\n            ...options,\n            routeGetIdList: {\n              ...options.routeGetIdList,\n              [action.payload.name]: getIdFunction(),\n            },\n          });\n\n          /**\n           * React Navigation doesn't support dynamic getId function on the action. Because of this,\n           * can you enter a state where the screen is pushed multiple times but the normal getStateForAction\n           * doesn't remove the duplicates. We need to filter the state to only have singular screens.\n           */\n          return actionSingularOptions\n            ? filterSingular(nextState, actionSingularOptions)\n            : nextState;\n        }\n        case 'NAVIGATE': {\n          /**\n           * NAVIGATE should push unless the current name & route params of the current and target screen match.\n           * Search params and hashes should be ignored.\n           *\n           * If the name, route params & search params match, no action is taken.\n           * If both the name and route params match, the screen is replaced.\n           * If the name / route params do not match, the screen is pushed.\n           *\n           * If 'getID' or 'singular' is set and a match is found, instead of pushing a new screen,\n           * the existing screen will be moved to the HEAD of the stack. If there are multiple matches, the rest will be removed.\n           */\n          const nextState = original.getStateForAction(state, action, {\n            ...options,\n            routeGetIdList: {\n              ...options.routeGetIdList,\n              [action.payload.name]: getIdFunction((options) => {\n                return getSingularId(action.payload.name, options);\n              }),\n            },\n          });\n\n          /**\n           * React Navigation doesn't support dynamic getId function on the action. Because of this,\n           * can you enter a state where the screen is pushed multiple times but the normal getStateForAction\n           * doesn't remove the duplicates. We need to filter the state to only have singular screens.\n           */\n          return actionSingularOptions\n            ? filterSingular(nextState, actionSingularOptions)\n            : nextState;\n        }\n        default: {\n          return original.getStateForAction(state, action, options);\n        }\n      }\n    },\n  };\n};\n\nfunction getActionSingularIdFn(\n  actionGetId: SingularOptions | undefined,\n  name: string\n): GetId | undefined {\n  if (typeof actionGetId === 'function') {\n    return (options) => actionGetId(name, options.params ?? {});\n  } else if (actionGetId === true) {\n    return (options) => getSingularId(name, options);\n  }\n\n  return undefined;\n}\n\n/**\n * If there is a dynamic singular on an action, then we need to filter the state to only have singular screens.\n * As multiples may have been added before we did the singular navigation.\n */\nfunction filterSingular<\n  T extends\n    | StackNavigationState<ParamListBase>\n    | PartialState<StackNavigationState<ParamListBase>>\n    | null,\n>(state: T, singular: SingularOptions): T {\n  if (!state || !singular) {\n    return state;\n  }\n\n  if (!state.routes) {\n    return state;\n  }\n\n  const currentIndex = state.index || state.routes.length - 1;\n  const current = state.routes[currentIndex];\n  const name = current.name;\n\n  const getId = getActionSingularIdFn(singular, name);\n\n  if (!getId) {\n    return state;\n  }\n\n  const id = getId({ params: current.params });\n\n  if (!id) {\n    return state;\n  }\n\n  // TypeScript needs a type assertion here for the filter to work.\n  let routes = state.routes as PartialRoute<Route<string, object | undefined>>[];\n  routes = routes.filter((route, index) => {\n    // If the route is the current route, keep it.\n    if (index === currentIndex) {\n      return true;\n    }\n\n    // Remove all other routes with the same name and id.\n    return name !== route.name || id !== getId({ params: route.params });\n  });\n\n  return {\n    ...state,\n    index: routes.length - 1,\n    routes,\n  };\n}\n\nconst Stack = Object.assign(\n  (props: ComponentProps<typeof RNStack>) => {\n    return <RNStack {...props} UNSTABLE_router={stackRouterOverride} />;\n  },\n  {\n    Screen: RNStack.Screen as (\n      props: ComponentProps<typeof RNStack.Screen> & { singular?: boolean }\n    ) => null,\n    Protected,\n  }\n);\n\nexport default Stack;\n\nexport const StackRouter: typeof RNStackRouter = (options) => {\n  const router = RNStackRouter(options);\n  return {\n    ...router,\n    ...stackRouterOverride(router),\n  };\n};\n"]}