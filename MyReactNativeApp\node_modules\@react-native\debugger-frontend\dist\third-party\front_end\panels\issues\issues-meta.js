import*as e from"../../core/common/common.js";import*as s from"../../core/i18n/i18n.js";import*as i from"../../models/issues_manager/issues_manager.js";import*as a from"../../ui/legacy/legacy.js";const r={issues:"Issues",showIssues:"Show Issues"},o=s.i18n.registerUIStrings("panels/issues/issues-meta.ts",r),n=s.i18n.getLazilyComputedLocalizedString.bind(void 0,o);let t;async function u(){return t||(t=await import("./issues.js")),t}a.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:n(r.issues),commandPrompt:n(r.showIssues),order:100,persistence:"closeable",loadView:async()=>new((await u()).IssuesPane.IssuesPane)}),e.Revealer.registerRevealer({contextTypes:()=>[i.Issue.Issue],destination:e.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>new((await u()).IssueRevealer.IssueRevealer)});
