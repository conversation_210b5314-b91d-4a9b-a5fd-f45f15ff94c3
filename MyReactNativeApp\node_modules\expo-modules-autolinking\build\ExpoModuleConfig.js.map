{"version": 3, "file": "ExpoModuleConfig.js", "sourceRoot": "", "sources": ["../src/ExpoModuleConfig.ts"], "names": [], "mappings": ";;;AAgLA,8EAIC;AA3KD,SAAS,QAAQ,CAAI,KAA0B;IAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACtC,CAAC;AAED,MAAa,wBAAwB;IAE1B;IACA;IACA;IACA;IACA;IACA;IAIA;IAVT,YACS,IAAY,EACZ,IAAY,EACZ,OAAkB,EAClB,WAAgC,EAChC,iBAAuD,EACvD,8BAAuC;IAC9C;;OAEG;IACI,YAAqB,KAAK;QAT1B,SAAI,GAAJ,IAAI,CAAQ;QACZ,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAW;QAClB,gBAAW,GAAX,WAAW,CAAqB;QAChC,sBAAiB,GAAjB,iBAAiB,CAAsC;QACvD,mCAA8B,GAA9B,8BAA8B,CAAS;QAIvC,cAAS,GAAT,SAAS,CAAiB;IAChC,CAAC;CACL;AAbD,4DAaC;AAED;;GAEG;AACH,MAAa,gBAAgB;IACN;IAArB,YAAqB,SAA8B;QAA9B,cAAS,GAAT,SAAS,CAAqB;IAAG,CAAC;IAEvD;;OAEG;IACH,gBAAgB,CAAC,QAA2B;QAC1C,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC;QAE1D,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,4EAA4E;YAC5E,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,EAAE;gBACnD,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,OAAO,WAAW,EAAE,OAAO,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE,sBAAsB,IAAI,EAAE,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE,qBAAqB,IAAI,EAAE,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,eAAe,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,IAAI,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,kBAA0B;QACxC,MAAM,eAAe,GAA+B,EAAE,CAAC;QAEvD,8DAA8D;QAC9D,eAAe,CAAC,IAAI,CAClB,IAAI,wBAAwB,CAC1B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,IAAI,kBAAkB,EAClD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,IAAI,SAAS,EACzC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAC/B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,EACnC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,iBAAiB,EACzC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,8BAA8B,EACtD,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,mDAAmD;SAClF,CACF,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACpD,eAAe,CAAC,IAAI,CAClB,IAAI,wBAAwB,CAC1B,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,iBAAiB,EACzB,OAAO,CAAC,8BAA8B,CACvC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,IAAI,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,iBAAiB,IAAI,EAAE,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF;AAzID,4CAyIC;AAED;;GAEG;AACH,SAAgB,iCAAiC,CAAC,IAAY;IAC5D,kDAAkD;IAClD,4DAA4D;IAC5D,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAwB,CAAC,CAAC;AACpE,CAAC", "sourcesContent": ["import {\n  AndroidGradleAarProjectDescriptor,\n  AndroidGradlePluginDescriptor,\n  AndroidPublication,\n  RawExpoModuleConfig,\n  RawModuleConfigApple,\n  SupportedPlatform,\n} from './types';\n\nfunction arrayize<T>(value: T[] | T | undefined): T[] {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value != null ? [value] : [];\n}\n\nexport class ExpoAndroidProjectConfig {\n  constructor(\n    public name: string,\n    public path: string,\n    public modules?: string[],\n    public publication?: AndroidPublication,\n    public gradleAarProjects?: AndroidGradleAarProjectDescriptor[],\n    public shouldUsePublicationScriptPath?: string,\n    /**\n     * Whether this project is the root one.\n     */\n    public isDefault: boolean = false\n  ) {}\n}\n\n/**\n * A class that wraps the raw config (`expo-module.json` or `unimodule.json`).\n */\nexport class ExpoModuleConfig {\n  constructor(readonly rawConfig: RawExpoModuleConfig) {}\n\n  /**\n   * Whether the module supports given platform.\n   */\n  supportsPlatform(platform: SupportedPlatform): boolean {\n    const supportedPlatforms = this.rawConfig.platforms ?? [];\n\n    if (platform === 'apple') {\n      // Apple platform is supported when any of iOS, macOS and tvOS is supported.\n      return supportedPlatforms.some((supportedPlatform) => {\n        return ['apple', 'ios', 'macos', 'tvos'].includes(supportedPlatform);\n      });\n    }\n    return supportedPlatforms.includes(platform);\n  }\n\n  /**\n   * Returns the generic config for all Apple platforms with a fallback to the legacy iOS config.\n   */\n  getAppleConfig(): RawModuleConfigApple | null {\n    return this.rawConfig.apple ?? this.rawConfig.ios ?? null;\n  }\n\n  /**\n   * Returns a list of names of Swift native modules classes to put to the generated modules provider file.\n   */\n  appleModules() {\n    const appleConfig = this.getAppleConfig();\n    return appleConfig?.modules ?? [];\n  }\n\n  /**\n   * Returns a list of names of Swift classes that receives AppDelegate life-cycle events.\n   */\n  appleAppDelegateSubscribers(): string[] {\n    return this.getAppleConfig()?.appDelegateSubscribers ?? [];\n  }\n\n  /**\n   * Returns a list of names of Swift classes that implement `ExpoReactDelegateHandler`.\n   */\n  appleReactDelegateHandlers(): string[] {\n    return this.getAppleConfig()?.reactDelegateHandlers ?? [];\n  }\n\n  /**\n   * Returns podspec paths defined by the module author.\n   */\n  applePodspecPaths(): string[] {\n    return arrayize(this.getAppleConfig()?.podspecPath);\n  }\n\n  /**\n   * Returns the product module names, if defined by the module author.\n   */\n  appleSwiftModuleNames(): string[] {\n    return arrayize(this.getAppleConfig()?.swiftModuleName);\n  }\n\n  /**\n   * Returns whether this module will be added only to the debug configuration\n   */\n  appleDebugOnly(): boolean {\n    return this.getAppleConfig()?.debugOnly ?? false;\n  }\n\n  /**\n   * Returns information about Android projects defined by the module author.\n   */\n  androidProjects(defaultProjectName: string): ExpoAndroidProjectConfig[] {\n    const androidProjects: ExpoAndroidProjectConfig[] = [];\n\n    // Adding the \"root\" Android project - it might not be valide.\n    androidProjects.push(\n      new ExpoAndroidProjectConfig(\n        this.rawConfig.android?.name ?? defaultProjectName,\n        this.rawConfig.android?.path ?? 'android',\n        this.rawConfig.android?.modules,\n        this.rawConfig.android?.publication,\n        this.rawConfig.android?.gradleAarProjects,\n        this.rawConfig.android?.shouldUsePublicationScriptPath,\n        !this.rawConfig.android?.path // it's default project because path is not defined\n      )\n    );\n\n    this.rawConfig.android?.projects?.forEach((project) => {\n      androidProjects.push(\n        new ExpoAndroidProjectConfig(\n          project.name,\n          project.path,\n          project.modules,\n          project.publication,\n          project.gradleAarProjects,\n          project.shouldUsePublicationScriptPath\n        )\n      );\n    });\n\n    return androidProjects;\n  }\n\n  /**\n   * Returns gradle plugins descriptors defined by the module author.\n   */\n  androidGradlePlugins(): AndroidGradlePluginDescriptor[] {\n    return arrayize(this.rawConfig.android?.gradlePlugins ?? []);\n  }\n\n  /**\n   * Returns gradle projects containing AAR files defined by the module author.\n   */\n  androidGradleAarProjects(): AndroidGradleAarProjectDescriptor[] {\n    return arrayize(this.rawConfig.android?.gradleAarProjects ?? []);\n  }\n\n  /**\n   * Returns the publication config for Android.\n   */\n  androidPublication(): AndroidPublication | undefined {\n    return this.rawConfig.android?.publication;\n  }\n\n  /**\n   * Returns core features required by the module author.\n   */\n  coreFeatures(): string[] {\n    return arrayize(this.rawConfig.coreFeatures ?? []);\n  }\n\n  /**\n   * Returns serializable raw config.\n   */\n  toJSON(): RawExpoModuleConfig {\n    return this.rawConfig;\n  }\n}\n\n/**\n * Reads the config at given path and returns the config wrapped by `ExpoModuleConfig` class.\n */\nexport function requireAndResolveExpoModuleConfig(path: string): ExpoModuleConfig {\n  // TODO: Validate the raw config against a schema.\n  // TODO: Support for `*.js` files, not only static `*.json`.\n  return new ExpoModuleConfig(require(path) as RawExpoModuleConfig);\n}\n"]}