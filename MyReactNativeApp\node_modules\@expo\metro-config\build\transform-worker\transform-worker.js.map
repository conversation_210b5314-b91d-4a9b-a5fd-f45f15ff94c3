{"version": 3, "file": "transform-worker.js", "sourceRoot": "", "sources": ["../../src/transform-worker/transform-worker.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCH,8BA2SC;AAlVD,0EAAkD;AAMlD,yCAA8C;AAE9C,+BAA2C;AAC3C,+CAKuB;AACvB,+DAAqD;AACrD,iEAAmD;AACnD,uCAAmD;AACnD,iCAAgD;AAEhD,gDAAgD;AAEhD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,oCAAoC,CAAuB,CAAC;AAE3F,SAAS,cAAc,CAAC,KAAU;IAChC,IAAI,CAAC,KAAK;QAAE,OAAO,SAAS,CAAC;IAC7B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;IACrF,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;AACrF,CAAC;AAEM,KAAK,UAAU,SAAS,CAC7B,MAA2B,EAC3B,WAAmB,EACnB,QAAgB,EAChB,IAAY,EACZ,OAA2B;IAE3B,MAAM,WAAW,GAAG,OAAO,CAAC,sBAAsB,EAAE,WAAW,KAAK,cAAc,CAAC;IACnF,MAAM,aAAa,GAAG,IAAA,sBAAW,EAAC,QAAQ,CAAC,CAAC;IAC5C,IACE,OAAO,OAAO,CAAC,sBAAsB,EAAE,GAAG,KAAK,QAAQ;QACvD,aAAa,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAC3C,CAAC;QACD,qGAAqG;QACrG,yCAAyC;QACzC,MAAM,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC;QAChG,MAAM,GAAG,GAAG,6DAA6D,yBAAyB,aAAa,CAAC;QAChH,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;IACpF,CAAC;IACD,IAAI,aAAa,CAAC,KAAK,CAAC,8BAA8B,CAAC,EAAE,CAAC;QACxD,MAAM,WAAW,GAAG,OAAO,CAAC,sBAAsB,EAAE,WAAW,CAAC;QAChE,MAAM,QAAQ,GAAG,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK,cAAc,CAAC;QAE1E,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;YAC1F,gFAAgF;YAChF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,KAAK,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,CAAC;gBAErD,gBAAgB;gBAChB,MAAM,GAAG,GACP,sBAAsB;oBACtB,gBAAgB;yBACb,GAAG,CAAC,CAAC,QAAgB,EAAE,EAAE;wBACxB,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;wBACpD,OAAO,6BAA6B,kBAAkB,aAAa,QAAQ,oBAAoB,kBAAkB,IAAI,CAAC;oBACxH,CAAC,CAAC;yBACD,IAAI,CAAC,IAAI,CAAC;oBACb,MAAM,CAAC;gBAET,OAAO,MAAM,CAAC,SAAS,CACrB,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,CAAC,IAAI,CAAC,+BAA+B,GAAG,GAAG,CAAC,EAClD,OAAO,CACR,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3E,yDAAyD;IACzD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,WAAW,GAAG,OAAO,CAAC,sBAAsB,EAAE,WAAW,CAAC;QAChE,MAAM,mBAAmB,GAAG,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK,cAAc,CAAC;QACrF,IACE,mBAAmB;YACnB,wCAAwC;YACxC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,OAAO,CAAC,QAAQ,yBAAyB,CAAC,CAAC;gBACvF,oBAAoB;gBACpB,QAAQ,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC,EACjE,CAAC;YACD,2GAA2G;YAC3G,OAAO,MAAM,CAAC,SAAS,CACrB,MAAM,EACN,WAAW,EACX,QAAQ,EACR,CAAC,OAAO,CAAC,MAAM;gBACb,CAAC,CAAC,MAAM,CAAC,IAAI;gBACT,sEAAsE;gBACtE,kBAAkB;gBAClB,6EAA6E,CAC9E;gBACH,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EACnB,OAAO,CACR,CAAC;QACJ,CAAC;QAED,IACE,mBAAmB;YACnB,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC;YACnC,QAAQ,CAAC,KAAK,CAAC,8CAA8C,CAAC,EAC9D,CAAC;YACD,iEAAiE;YACjE,oFAAoF;YACpF,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QACnF,CAAC;QAED,sHAAsH;QACtH,IAAI,QAAQ,CAAC,KAAK,CAAC,6DAA6D,CAAC,EAAE,CAAC;YAClF,MAAM,aAAa,GAAG,IAAA,kCAAY,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,mBAAmB,CAAC,CAAC;YAChF,OAAO,MAAM,CAAC,SAAS,CACrB,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,EAC/D,OAAO,CACR,CAAC;QACJ,CAAC;QAED;QACE,uDAAuD;QACvD,CAAC,mBAAmB;YACpB,QAAQ,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAC/C,CAAC;YACD,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QACnF,CAAC;QACD;QACE,uFAAuF;QACvF,mBAAmB;YACnB,sCAAsC;YACtC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,CAAC,EAC3C,CAAC;YACD;YACE,sGAAsG;YACtG,OAAO,CAAC,GAAG,EACX,CAAC;gBACD,MAAM,YAAY,GAAG,IAAA,oBAAQ,EAAC,IAAA,mBAAO,EAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,CAAC;gBAC9D,MAAM,SAAS,GAAG,IAAA,sBAAW,EAAC,YAAY,CAAC,CAAC;gBAE5C,6HAA6H;gBAC7H,4CAA4C;gBAC5C,uHAAuH;gBACvH,mFAAmF;gBACnF,+HAA+H;gBAC/H,mKAAmK;gBACnK,MAAM,QAAQ,GAAG,yCAAyC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;;;;cAI7E,CAAC;gBACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;YACzF,CAAC;iBAAM,CAAC;gBACN,6IAA6I;gBAE7I,uGAAuG;gBACvG,MAAM,QAAQ,GAAG;;;;;WAKd,CAAC;gBACJ,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;YACzF,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED,2DAA2D;IAC3D,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;QAC/B,MAAM,IAAI,GAAG,IAAA,4BAAc,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvF,OAAO,MAAM,CAAC,SAAS,CACrB,MAAM,EACN,WAAW,EACX,QAAQ;QACR,2BAA2B;QAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EACjB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAEjC,2BAA2B;IAC3B,MAAM,cAAc,GAAG,MAAM,IAAA,gCAAsB,EAAC,WAAW,EAAE;QAC/D,GAAG,EAAE,IAAI;QACT,QAAQ;KACT,CAAC,CAAC;IAEH,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;QAC9B,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC;IAC5B,CAAC;IAED,uEAAuE;IACvE,MAAM,MAAM,GAAG,IAAA,gBAAS,EAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,MAAM,EAAE,CAAC;QACX,IAAI,GAAG,IAAA,kBAAW,EAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC;IAC3E,CAAC;IAED,gEAAgE;IAChE,sDAAsD;IACtD,IAAI,IAAA,4BAAc,EAAC,QAAQ,CAAC,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,MAAM,IAAA,mCAAqB,EAAC;YAC1C,oFAAoF;YACpF,4EAA4E;YAC5E,QAAQ,EAAE,aAAa;YACvB,GAAG,EAAE,IAAI;YACT,OAAO,EAAE;gBACP,WAAW;gBACX,WAAW;gBACX,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,SAAS,EAAE,KAAK;aACjB;SACF,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAC5C,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAC3B,OAAO,CACR,CAAC;QAEF,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QACvC,MAAM,MAAM,GAAmB;YAC7B;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE;oBACJ,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI;oBAElC,wDAAwD;oBACxD,GAAG,EAAE;wBACH,IAAI,EAAE,OAAO;wBACb,SAAS,EAAE,IAAA,oBAAU,EAAC,OAAO,CAAC;wBAC9B,GAAG,EAAE,EAAE;wBACP,WAAW,EAAE,IAAI;wBACjB,sFAAsF;wBACtF,qEAAqE;wBACrE,SAAS,EAAE,cAAc,CAAC,UAAU;wBACpC,eAAe,EAAE,OAAO,CAAC,eAAe;qBACzC;iBACF;aACF;SACF,CAAC;QAEF,OAAO;YACL,YAAY,EAAE,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;YACvE,MAAM;SACP,CAAC;IACJ,CAAC;IAED,cAAc;IAEd,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,cAAc,CAAkC,CAAC;IAE/E,iFAAiF;IACjF,gDAAgD;IAChD,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,QAAQ;QACR,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACvB,aAAa,EAAE,IAAI;QACnB,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,KAAK;QACjB,WAAW;QACX,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,mBAAmB,EAAE,IAAI;QACzB,mEAAmE;QACnE,QAAQ,EAAE,OAAO,CAAC,gBAAgB;KACnC,CAAC,CAAC;IAEH,IAAA,8BAAgB,EAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IAEtD,MAAM,UAAU,GAAG,IAAA,+BAAiB,EAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,UAAU,CAAC,CAAC;IAC7F,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;IAEhC,wDAAwD;IACxD,MAAM,SAAS,GAA0C;QACvD,IAAI,EAAE,OAAO;QACb,SAAS,EAAE,IAAA,oBAAU,EAAC,OAAO,CAAC;QAC9B,GAAG,EAAE,EAAE;QACP,WAAW,EAAE,IAAI;QACjB,sFAAsF;QACtF,qEAAqE;QACrE,SAAS,EAAE,cAAc,CAAC,UAAU;QACpC,eAAe,EAAE,UAAU,CAAC,eAAe;KAC5C,CAAC;IAEF,wDAAwD;IACxD,kDAAkD;IAClD,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAC5C,MAAM,EACN,WAAW,EACX,QAAQ,EACR,OAAO,CAAC,GAAG;QACT,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,wBAAkB,EAAC,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC;QAC1E,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EACnB,OAAO,CACR,CAAC;IAEF,iFAAiF;IACjF,qFAAqF;IACrF,oCAAoC;IACpC,MAAM,MAAM,GAAmB;QAC7B;YACE,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE;gBACJ,GAAI,eAAe,CAAC,MAAM,CAAC,CAAC,CAAkB,CAAC,IAAI;gBACnD,GAAG,EAAE,SAAS;aACf;SACF;KACF,CAAC;IAEF,OAAO;QACL,YAAY,EAAE,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC;QAC1E,MAAM;KACP,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,OAAO,GAAG;IACf,iDAAiD;IACjD,GAAG,MAAM;IACT,SAAS;CACV,CAAC"}