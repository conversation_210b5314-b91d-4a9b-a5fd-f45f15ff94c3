{"version": 3, "names": ["_convertSourceMap", "data", "require", "_generator", "_mergeMap", "generateCode", "pluginPasses", "file", "opts", "ast", "code", "inputMap", "generatorOpts", "inputSourceMap", "toObject", "results", "plugins", "plugin", "generatorOverride", "result", "generate", "undefined", "push", "length", "then", "Error", "outputCode", "decodedMap", "outputMap", "map", "__mergedMap", "Object", "assign", "mergeSourceMap", "sourceFileName", "sourceMaps", "convertSourceMap", "fromObject", "toComment"], "sources": ["../../../src/transformation/file/generate.ts"], "sourcesContent": ["import type { PluginPasses } from \"../../config/index.ts\";\nimport convertSourceMap from \"convert-source-map\";\nimport type { GeneratorResult } from \"@babel/generator\";\nimport generate from \"@babel/generator\";\n\nimport type File from \"./file.ts\";\nimport mergeSourceMap from \"./merge-map.ts\";\n\nexport default function generateCode(\n  pluginPasses: PluginPasses,\n  file: File,\n): {\n  outputCode: string;\n  outputMap: GeneratorResult[\"map\"] | null;\n} {\n  const { opts, ast, code, inputMap } = file;\n  const { generatorOpts } = opts;\n\n  generatorOpts.inputSourceMap = inputMap?.toObject();\n\n  const results = [];\n  for (const plugins of pluginPasses) {\n    for (const plugin of plugins) {\n      const { generatorOverride } = plugin;\n      if (generatorOverride) {\n        const result = generatorOverride(ast, generatorOpts, code, generate);\n\n        if (result !== undefined) results.push(result);\n      }\n    }\n  }\n\n  let result;\n  if (results.length === 0) {\n    result = generate(ast, generatorOpts, code);\n  } else if (results.length === 1) {\n    result = results[0];\n\n    if (typeof result.then === \"function\") {\n      throw new Error(\n        `You appear to be using an async codegen plugin, ` +\n          `which your current version of Babel does not support. ` +\n          `If you're using a published plugin, ` +\n          `you may need to upgrade your @babel/core version.`,\n      );\n    }\n  } else {\n    throw new Error(\"More than one plugin attempted to override codegen.\");\n  }\n\n  // Decoded maps are faster to merge, so we attempt to get use the decodedMap\n  // first. But to preserve backwards compat with older Generator, we'll fall\n  // back to the encoded map.\n  let { code: outputCode, decodedMap: outputMap = result.map } = result;\n\n  // For backwards compat.\n  if (result.__mergedMap) {\n    /**\n     * @see mergeSourceMap\n     */\n    outputMap = { ...result.map };\n  } else {\n    if (outputMap) {\n      if (inputMap) {\n        // mergeSourceMap returns an encoded map\n        outputMap = mergeSourceMap(\n          inputMap.toObject(),\n          outputMap,\n          generatorOpts.sourceFileName,\n        );\n      } else {\n        // We cannot output a decoded map, so retrieve the encoded form. Because\n        // the decoded form is free, it's fine to prioritize decoded first.\n        outputMap = result.map;\n      }\n    }\n  }\n\n  if (opts.sourceMaps === \"inline\" || opts.sourceMaps === \"both\") {\n    outputCode += \"\\n\" + convertSourceMap.fromObject(outputMap).toComment();\n  }\n\n  if (opts.sourceMaps === \"inline\") {\n    outputMap = null;\n  }\n\n  return { outputCode, outputMap };\n}\n"], "mappings": ";;;;;;AACA,SAAAA,kBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,iBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,IAAAG,SAAA,GAAAF,OAAA;AAEe,SAASG,YAAYA,CAClCC,YAA0B,EAC1BC,IAAU,EAIV;EACA,MAAM;IAAEC,IAAI;IAAEC,GAAG;IAAEC,IAAI;IAAEC;EAAS,CAAC,GAAGJ,IAAI;EAC1C,MAAM;IAAEK;EAAc,CAAC,GAAGJ,IAAI;EAE9BI,aAAa,CAACC,cAAc,GAAGF,QAAQ,oBAARA,QAAQ,CAAEG,QAAQ,CAAC,CAAC;EAEnD,MAAMC,OAAO,GAAG,EAAE;EAClB,KAAK,MAAMC,OAAO,IAAIV,YAAY,EAAE;IAClC,KAAK,MAAMW,MAAM,IAAID,OAAO,EAAE;MAC5B,MAAM;QAAEE;MAAkB,CAAC,GAAGD,MAAM;MACpC,IAAIC,iBAAiB,EAAE;QACrB,MAAMC,MAAM,GAAGD,iBAAiB,CAACT,GAAG,EAAEG,aAAa,EAAEF,IAAI,EAAEU,oBAAQ,CAAC;QAEpE,IAAID,MAAM,KAAKE,SAAS,EAAEN,OAAO,CAACO,IAAI,CAACH,MAAM,CAAC;MAChD;IACF;EACF;EAEA,IAAIA,MAAM;EACV,IAAIJ,OAAO,CAACQ,MAAM,KAAK,CAAC,EAAE;IACxBJ,MAAM,GAAG,IAAAC,oBAAQ,EAACX,GAAG,EAAEG,aAAa,EAAEF,IAAI,CAAC;EAC7C,CAAC,MAAM,IAAIK,OAAO,CAACQ,MAAM,KAAK,CAAC,EAAE;IAC/BJ,MAAM,GAAGJ,OAAO,CAAC,CAAC,CAAC;IAEnB,IAAI,OAAOI,MAAM,CAACK,IAAI,KAAK,UAAU,EAAE;MACrC,MAAM,IAAIC,KAAK,CACb,kDAAkD,GAChD,wDAAwD,GACxD,sCAAsC,GACtC,mDACJ,CAAC;IACH;EACF,CAAC,MAAM;IACL,MAAM,IAAIA,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAKA,IAAI;IAAEf,IAAI,EAAEgB,UAAU;IAAEC,UAAU,EAAEC,SAAS,GAAGT,MAAM,CAACU;EAAI,CAAC,GAAGV,MAAM;EAGrE,IAAIA,MAAM,CAACW,WAAW,EAAE;IAItBF,SAAS,GAAAG,MAAA,CAAAC,MAAA,KAAQb,MAAM,CAACU,GAAG,CAAE;EAC/B,CAAC,MAAM;IACL,IAAID,SAAS,EAAE;MACb,IAAIjB,QAAQ,EAAE;QAEZiB,SAAS,GAAG,IAAAK,iBAAc,EACxBtB,QAAQ,CAACG,QAAQ,CAAC,CAAC,EACnBc,SAAS,EACThB,aAAa,CAACsB,cAChB,CAAC;MACH,CAAC,MAAM;QAGLN,SAAS,GAAGT,MAAM,CAACU,GAAG;MACxB;IACF;EACF;EAEA,IAAIrB,IAAI,CAAC2B,UAAU,KAAK,QAAQ,IAAI3B,IAAI,CAAC2B,UAAU,KAAK,MAAM,EAAE;IAC9DT,UAAU,IAAI,IAAI,GAAGU,kBAAeA,CAAC,CAACC,UAAU,CAACT,SAAS,CAAC,CAACU,SAAS,CAAC,CAAC;EACzE;EAEA,IAAI9B,IAAI,CAAC2B,UAAU,KAAK,QAAQ,EAAE;IAChCP,SAAS,GAAG,IAAI;EAClB;EAEA,OAAO;IAAEF,UAAU;IAAEE;EAAU,CAAC;AAClC;AAAC", "ignoreList": []}