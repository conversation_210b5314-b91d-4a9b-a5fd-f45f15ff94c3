{"version": 3, "file": "StatusBar.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_Colors", "_Styles", "_androidPlugins", "e", "__esModule", "default", "COLOR_PRIMARY_DARK_KEY", "WINDOW_LIGHT_STATUS_BAR", "STATUS_BAR_COLOR", "withStatusBar", "config", "withStatusBarColors", "withStatusBarStyles", "exports", "withAndroidColors", "modResults", "setStatusBarColors", "withAndroidStyles", "setStatusBarStyles", "colors", "assignColorValue", "name", "value", "getStatusBarColor", "styles", "hexString", "floatElement", "getStatusBarTranslucent", "assignStylesValue", "parent", "getAppThemeGroup", "add", "getStatusBarStyle", "backgroundColor", "androidStatusBar", "assert", "translucent", "barStyle"], "sources": ["../../src/android/StatusBar.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport assert from 'assert';\n\nimport { assignColorValue } from './Colors';\nimport { ResourceXML } from './Resources';\nimport { assignStylesValue, getAppThemeGroup } from './Styles';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAndroidColors, withAndroidStyles } from '../plugins/android-plugins';\n\n// https://developer.android.com/reference/android/R.attr#colorPrimaryDark\nconst COLOR_PRIMARY_DARK_KEY = 'colorPrimaryDark';\n// https://developer.android.com/reference/android/R.attr#windowLightStatusBar\nconst WINDOW_LIGHT_STATUS_BAR = 'android:windowLightStatusBar';\n// https://developer.android.com/reference/android/R.attr#statusBarColor\nconst STATUS_BAR_COLOR = 'android:statusBarColor';\n\nexport const withStatusBar: ConfigPlugin = (config) => {\n  config = withStatusBarColors(config);\n  config = withStatusBarStyles(config);\n  return config;\n};\n\nconst withStatusBarColors: ConfigPlugin = (config) => {\n  return withAndroidColors(config, (config) => {\n    config.modResults = setStatusBarColors(config, config.modResults);\n    return config;\n  });\n};\n\nconst withStatusBarStyles: ConfigPlugin = (config) => {\n  return withAndroidStyles(config, (config) => {\n    config.modResults = setStatusBarStyles(config, config.modResults);\n    return config;\n  });\n};\n\nexport function setStatusBarColors(\n  config: Pick<ExpoConfig, 'androidStatusBar'>,\n  colors: ResourceXML\n): ResourceXML {\n  return assignColorValue(colors, {\n    name: COLOR_PRIMARY_DARK_KEY,\n    value: getStatusBarColor(config),\n  });\n}\n\nexport function setStatusBarStyles(\n  config: Pick<ExpoConfig, 'androidStatusBar'>,\n  styles: ResourceXML\n): ResourceXML {\n  const hexString = getStatusBarColor(config);\n  const floatElement = getStatusBarTranslucent(config);\n\n  styles = assignStylesValue(styles, {\n    parent: getAppThemeGroup(),\n    name: WINDOW_LIGHT_STATUS_BAR,\n    value: 'true',\n    // Default is light-content, don't need to do anything to set it\n    add: getStatusBarStyle(config) === 'dark-content',\n  });\n\n  styles = assignStylesValue(styles, {\n    parent: getAppThemeGroup(),\n    name: STATUS_BAR_COLOR,\n    value: floatElement ? '@android:color/transparent' : (hexString ?? '@color/colorPrimaryDark'),\n    // Remove the color if translucent is used\n    add: floatElement || !!hexString,\n  });\n\n  return styles;\n}\n\nexport function getStatusBarColor(config: Pick<ExpoConfig, 'androidStatusBar'>) {\n  const backgroundColor = config.androidStatusBar?.backgroundColor;\n  if (backgroundColor) {\n    // Drop support for translucent\n    assert(\n      backgroundColor !== 'translucent',\n      `androidStatusBar.backgroundColor must be a valid hex string, instead got: \"${backgroundColor}\"`\n    );\n  }\n  return backgroundColor;\n}\n\n/**\n * Specifies whether the status bar should be \"translucent\". When true, the status bar is drawn with `position: absolute` and a gray underlay, when false `position: relative` (pushes content down).\n *\n * @default false\n * @param config\n * @returns\n */\nexport function getStatusBarTranslucent(config: Pick<ExpoConfig, 'androidStatusBar'>): boolean {\n  return config.androidStatusBar?.translucent ?? false;\n}\n\nexport function getStatusBarStyle(config: Pick<ExpoConfig, 'androidStatusBar'>) {\n  return config.androidStatusBar?.barStyle || 'light-content';\n}\n"], "mappings": ";;;;;;;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,gBAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,eAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkF,SAAAC,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAElF;AACA,MAAMG,sBAAsB,GAAG,kBAAkB;AACjD;AACA,MAAMC,uBAAuB,GAAG,8BAA8B;AAC9D;AACA,MAAMC,gBAAgB,GAAG,wBAAwB;AAE1C,MAAMC,aAA2B,GAAIC,MAAM,IAAK;EACrDA,MAAM,GAAGC,mBAAmB,CAACD,MAAM,CAAC;EACpCA,MAAM,GAAGE,mBAAmB,CAACF,MAAM,CAAC;EACpC,OAAOA,MAAM;AACf,CAAC;AAACG,OAAA,CAAAJ,aAAA,GAAAA,aAAA;AAEF,MAAME,mBAAiC,GAAID,MAAM,IAAK;EACpD,OAAO,IAAAI,mCAAiB,EAACJ,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACK,UAAU,GAAGC,kBAAkB,CAACN,MAAM,EAAEA,MAAM,CAACK,UAAU,CAAC;IACjE,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAME,mBAAiC,GAAIF,MAAM,IAAK;EACpD,OAAO,IAAAO,mCAAiB,EAACP,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACK,UAAU,GAAGG,kBAAkB,CAACR,MAAM,EAAEA,MAAM,CAACK,UAAU,CAAC;IACjE,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAEM,SAASM,kBAAkBA,CAChCN,MAA4C,EAC5CS,MAAmB,EACN;EACb,OAAO,IAAAC,0BAAgB,EAACD,MAAM,EAAE;IAC9BE,IAAI,EAAEf,sBAAsB;IAC5BgB,KAAK,EAAEC,iBAAiB,CAACb,MAAM;EACjC,CAAC,CAAC;AACJ;AAEO,SAASQ,kBAAkBA,CAChCR,MAA4C,EAC5Cc,MAAmB,EACN;EACb,MAAMC,SAAS,GAAGF,iBAAiB,CAACb,MAAM,CAAC;EAC3C,MAAMgB,YAAY,GAAGC,uBAAuB,CAACjB,MAAM,CAAC;EAEpDc,MAAM,GAAG,IAAAI,2BAAiB,EAACJ,MAAM,EAAE;IACjCK,MAAM,EAAE,IAAAC,0BAAgB,EAAC,CAAC;IAC1BT,IAAI,EAAEd,uBAAuB;IAC7Be,KAAK,EAAE,MAAM;IACb;IACAS,GAAG,EAAEC,iBAAiB,CAACtB,MAAM,CAAC,KAAK;EACrC,CAAC,CAAC;EAEFc,MAAM,GAAG,IAAAI,2BAAiB,EAACJ,MAAM,EAAE;IACjCK,MAAM,EAAE,IAAAC,0BAAgB,EAAC,CAAC;IAC1BT,IAAI,EAAEb,gBAAgB;IACtBc,KAAK,EAAEI,YAAY,GAAG,4BAA4B,GAAID,SAAS,IAAI,yBAA0B;IAC7F;IACAM,GAAG,EAAEL,YAAY,IAAI,CAAC,CAACD;EACzB,CAAC,CAAC;EAEF,OAAOD,MAAM;AACf;AAEO,SAASD,iBAAiBA,CAACb,MAA4C,EAAE;EAC9E,MAAMuB,eAAe,GAAGvB,MAAM,CAACwB,gBAAgB,EAAED,eAAe;EAChE,IAAIA,eAAe,EAAE;IACnB;IACA,IAAAE,iBAAM,EACJF,eAAe,KAAK,aAAa,EACjC,8EAA8EA,eAAe,GAC/F,CAAC;EACH;EACA,OAAOA,eAAe;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASN,uBAAuBA,CAACjB,MAA4C,EAAW;EAC7F,OAAOA,MAAM,CAACwB,gBAAgB,EAAEE,WAAW,IAAI,KAAK;AACtD;AAEO,SAASJ,iBAAiBA,CAACtB,MAA4C,EAAE;EAC9E,OAAOA,MAAM,CAACwB,gBAAgB,EAAEG,QAAQ,IAAI,eAAe;AAC7D", "ignoreList": []}