{"version": 3, "file": "storeContext.js", "sourceRoot": "", "sources": ["../../src/global-state/storeContext.ts"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AACb,iCAA2C;AAI9B,QAAA,YAAY,GAAG,IAAA,qBAAa,EAAqB,IAAI,CAAC,CAAC;AAE7D,MAAM,kBAAkB,GAAG,GAAG,EAAE,CAAC,IAAA,WAAG,EAAC,oBAAY,CAAE,CAAC;AAA9C,QAAA,kBAAkB,sBAA4B", "sourcesContent": ["'use client';\nimport { createContext, use } from 'react';\n\nimport { RouterStore } from './router-store';\n\nexport const StoreContext = createContext<RouterStore | null>(null);\n\nexport const useExpoRouterStore = () => use(StoreContext)!;\n"]}