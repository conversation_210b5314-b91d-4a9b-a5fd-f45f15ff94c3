/**
 * Fixes sort order of prop types.
 *
 * @param {Context} context the second element to compare.
 * @param {Fixer} fixer the first element to compare.
 * @param {Array} declarations The context of the two nodes.
 * @param {boolean=} ignoreCase whether or not to ignore case when comparing the two elements.
 * @param {boolean=} requiredFirst whether or not to sort required elements first.
 * @param {boolean=} callbacksLast whether or not to sort callbacks after everything else.
 * @param {boolean=} noSortAlphabetically whether or not to disable alphabetical sorting of the elements.
 * @param {boolean=} sortShapeProp whether or not to sort propTypes defined in PropTypes.shape.
 * @param {boolean=} checkTypes whether or not sorting of prop type definitions are checked.
 * @returns {Object|*|{range, text}} the sort order of the two elements.
 */
export function fixPropTypesSort(context: Context, fixer: Fixer, declarations: any[], ignoreCase?: boolean | undefined, requiredFirst?: boolean | undefined, callbacksLast?: boolean | undefined, noSortAlphabetically?: boolean | undefined, sortShapeProp?: boolean | undefined, checkTypes?: boolean | undefined): any | any | {
    range;
    text;
};
/**
 * Checks if the proptype is a callback by checking if it starts with 'on'.
 *
 * @param {string} propName the name of the proptype to check.
 * @returns {boolean} true if the proptype is a callback.
 */
export function isCallbackPropName(propName: string): boolean;
/**
 * Checks if the prop is required or not.
 *
 * @param {ASTNode} node the prop to check.
 * @returns {boolean} true if the prop is required.
 */
export function isRequiredProp(node: ASTNode): boolean;
/**
 * Checks if the prop is PropTypes.shape.
 *
 * @param {ASTNode} node the prop to check.
 * @returns {boolean} true if the prop is PropTypes.shape.
 */
export function isShapeProp(node: ASTNode): boolean;
//# sourceMappingURL=propTypesSort.d.ts.map