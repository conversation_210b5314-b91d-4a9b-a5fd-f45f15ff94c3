{"version": 3, "file": "SimpleLineIcons.js", "sourceRoot": "", "sources": ["../src/SimpleLineIcons.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,8DAA8D,CAAC;AAChF,OAAO,QAAQ,MAAM,mEAAmE,CAAC;AAEzF,eAAe,aAAa,CAAC,QAAQ,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/SimpleLineIcons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/SimpleLineIcons.json';\n\nexport default createIconSet(glyphMap, 'simple-line-icons', font);\n"]}