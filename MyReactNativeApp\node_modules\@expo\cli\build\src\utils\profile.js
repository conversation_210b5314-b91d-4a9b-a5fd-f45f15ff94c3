"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "profile", {
    enumerable: true,
    get: function() {
        return profile;
    }
});
function _chalk() {
    const data = /*#__PURE__*/ _interop_require_default(require("chalk"));
    _chalk = function() {
        return data;
    };
    return data;
}
const _env = require("./env");
const _log = /*#__PURE__*/ _interop_require_wildcard(require("../log"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function profile(fn, functionName = fn.name) {
    if (!_env.env.EXPO_PROFILE) {
        return fn;
    }
    const name = _chalk().default.dim(`⏱  [profile] ${functionName ?? 'unknown'}`);
    return (...args)=>{
        // Start the timer.
        _log.time(name);
        // Invoke the method.
        const results = fn(...args);
        // If non-promise then return as-is.
        if (!(results instanceof Promise)) {
            _log.timeEnd(name);
            return results;
        }
        // Otherwise await to profile after the promise resolves.
        return new Promise((resolve, reject)=>{
            results.then((results)=>{
                resolve(results);
                _log.timeEnd(name);
            }, (reason)=>{
                reject(reason);
                _log.timeEnd(name);
            });
        });
    };
}

//# sourceMappingURL=profile.js.map