{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/autolinking/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AA6BA,0FAQC;AAKD,kDAEC;AA5CD,gDAAwB;AAExB,+CAAiD;AAY/C,iGAZO,8BAAgB,OAYP;AAXlB,+DAK+B;AAO7B,+GAXA,oDAA8B,OAWA;AAC9B,yGAVA,8CAAwB,OAUA;AAGxB,wGAZA,6CAAuB,OAYA;AAVzB,qDAA2F;AAQzF,mHARO,mDAAkC,OAQP;AAClC,oGAT2C,oCAAmB,OAS3C;AAPrB,yDAAsD;AASpD,iGATO,mCAAgB,OASP;AAElB,6DAA+F;AAAtF,mIAAA,4BAA4B,OAAA;AAAE,+HAAA,wBAAwB,OAAA;AAC/D,6DAA4D;AAAnD,0HAAA,mBAAmB,OAAA;AAC5B,2CAAyB;AAEzB;;GAEG;AACI,KAAK,UAAU,uCAAuC,CAC3D,WAAmB,EACnB,OAAwE;IAExE,MAAM,WAAW,GAAG,MAAM,IAAA,6CAAuB,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACrE,MAAM,WAAW,GAAG,MAAM,IAAA,8CAAwB,EAAC,EAAE,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,CAAC;IAC7F,MAAM,aAAa,GAAG,MAAM,IAAA,8BAAgB,EAAC,WAAW,CAAC,CAAC;IAC1D,OAAO,MAAM,IAAA,oCAAmB,EAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,MAAc,OAAO,CAAC,GAAG,EAAE;IAC7D,OAAO,cAAI,CAAC,OAAO,CAAC,IAAA,mDAA6B,EAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,CAAC", "sourcesContent": ["import path from 'path';\n\nimport { findModulesAsync } from './findModules';\nimport {\n  getProjectPackageJsonPathAsync,\n  getProjectPackageJsonPathSync,\n  mergeLinkingOptionsAsync,\n  resolveSearchPathsAsync,\n} from './mergeLinkingOptions';\nimport { resolveExtraBuildDependenciesAsync, resolveModulesAsync } from './resolveModules';\nimport type { ModuleDescriptor, SearchOptions } from '../types';\nimport { getConfiguration } from './getConfiguration';\n\nexport {\n  findModulesAsync,\n  getProjectPackageJsonPathAsync,\n  mergeLinkingOptionsAsync,\n  resolveExtraBuildDependenciesAsync,\n  resolveModulesAsync,\n  resolveSearchPathsAsync,\n  getConfiguration,\n};\nexport { generateModulesProviderAsync, generatePackageListAsync } from './generatePackageList';\nexport { verifySearchResults } from './verifySearchResults';\nexport * from '../types';\n\n/**\n * Programmatic API to query autolinked modules for a project.\n */\nexport async function queryAutolinkingModulesFromProjectAsync(\n  projectRoot: string,\n  options: Pick<SearchOptions, 'platform' | 'exclude' | 'onlyProjectDeps'>\n): Promise<ModuleDescriptor[]> {\n  const searchPaths = await resolveSearchPathsAsync(null, projectRoot);\n  const linkOptions = await mergeLinkingOptionsAsync({ ...options, projectRoot, searchPaths });\n  const searchResults = await findModulesAsync(linkOptions);\n  return await resolveModulesAsync(searchResults, linkOptions);\n}\n\n/**\n * Get the project root directory from the current working directory.\n */\nexport function findProjectRootSync(cwd: string = process.cwd()): string {\n  return path.dirname(getProjectPackageJsonPathSync(cwd));\n}\n"]}