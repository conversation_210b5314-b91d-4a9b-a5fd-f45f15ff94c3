<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/writable_streambuffer.js</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../prettify.css">
    <link rel="stylesheet" href="../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">lib/writable_streambuffer.js</span></h1>
    <h2>
        Statements: <span class="metric">96.77% <small>(60 / 62)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">90.63% <small>(29 / 32)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">100% <small>(9 / 9)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">98.25% <small>(56 / 57)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric"><span class="ignore-none">none</span></span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../index.html">All files</a> &#187; <a href="index.html">lib/</a> &#187; writable_streambuffer.js</div>
</div>
<div class="body">
<pre><table class="coverage">
<tr><td class="line-count">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98</td><td class="line-coverage"><span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">5</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">5</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-yes">7</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">11</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-yes">2</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-yes">8</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-yes">4</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">var util = require("util"),
	stream = require("stream"),
	constants = require("./constants");
&nbsp;
// TODO: clear up specs on returning false from a write and emitting a drain event.
// Does this mean if I return false from a write, I should ignore any write requests between that false return and the drain event?
var WritableStreamBuffer = module.exports = function(opts) {
	var that = this;
&nbsp;
	stream.Stream.call(this);
&nbsp;
	opts = opts || {};
	var initialSize = opts.initialSize || constants.DEFAULT_INITIAL_SIZE;
	var incrementAmount = opts.incrementAmount || constants.DEFAULT_INCREMENT_AMOUNT;
&nbsp;
	var buffer = new Buffer(initialSize);
	var size = 0;
&nbsp;
	this.writable = true;
	this.readable = false;
&nbsp;
	this.size = function() {
		return size;
	};
&nbsp;
	this.maxSize = function() {
		return buffer.length;
	};
&nbsp;
	this.getContents = function(length) {
		if(!size) return false;
&nbsp;
		var data = new Buffer(Math.min(length || size, size));
		buffer.copy(data, 0, 0, data.length);
&nbsp;
		<span class="missing-if-branch" title="if path not taken" >I</span>if(data.length &lt; size)
<span class="cstat-no" title="statement not covered" >			buffer.copy(buffer, 0, data.length);</span>
&nbsp;
		size -= data.length;
&nbsp;
		return data;
	};
&nbsp;
	this.getContentsAsString = function(encoding, length) {
		if(!size) return false;
&nbsp;
		var data = buffer.toString(encoding || "utf8", 0, Math.min(length || size, size));
		var dataLength = Buffer.byteLength(data);
&nbsp;
		if(dataLength &lt; size)
			buffer.copy(buffer, 0, dataLength);
&nbsp;
		size -= dataLength;
		return data;
	};
&nbsp;
	var increaseBufferIfNecessary = function(incomingDataSize) {
		if((buffer.length - size) &lt; incomingDataSize) {
			var factor = Math.ceil((incomingDataSize - (buffer.length - size)) / incrementAmount);
&nbsp;
			var newBuffer = new Buffer(buffer.length + (incrementAmount * factor));
			buffer.copy(newBuffer, 0, 0, size);
			buffer = newBuffer;
		}
	};
&nbsp;
	this.write = function(data, encoding, callback) {
		if(!that.writable) return;
&nbsp;
		if(Buffer.isBuffer(data)) {
			increaseBufferIfNecessary(data.length);
			data.copy(buffer, size, 0);
			size += data.length;
		}
		else {
			data = data + "";
			increaseBufferIfNecessary(Buffer.byteLength(data));
			buffer.write(data, size, encoding || "utf8");
			size += Buffer.byteLength(data);
		}
		
		<span class="missing-if-branch" title="if path not taken" >I</span>if(typeof callback === "function") { <span class="cstat-no" title="statement not covered" >callback() ;}</span>
	};
&nbsp;
	this.end = function() {
		var args = Array.prototype.slice.apply(arguments);
		<span class="missing-if-branch" title="else path not taken" >E</span>if(args.length) that.write.apply(that, args);
		that.emit('finish');
		that.destroy();
	};
&nbsp;
	this.destroySoon = this.destroy = function() {
		that.writable = false;
		that.emit("close");
	};
};
util.inherits(WritableStreamBuffer, stream.Stream);
&nbsp;</pre></td></tr>
</table></pre>

</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Wed Jul 01 2015 04:16:19 GMT+0000 (UTC)</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
