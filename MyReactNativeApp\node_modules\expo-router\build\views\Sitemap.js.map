{"version": 3, "file": "Sitemap.js", "sourceRoot": "", "sources": ["../../src/views/Sitemap.tsx"], "names": [], "mappings": ";AAAA,mCAAmC;AACnC,YAAY,CAAC;;;;;AAiBb,sCAiCC;AAED,0BASC;AA1DD,kDAA0B;AAC1B,+CAA8F;AAC9F,mFAA8D;AAE9D,2CAAwC;AACxC,oCAAiD;AACjD,+DAAqD;AACrD,sDAA2C;AAC3C,uCAAoC;AACpC,0CAAwD;AACxD,kDAAkE;AAElE,MAAM,MAAM,GAAG,EAAE,CAAC;AAElB,SAAgB,aAAa;IAC3B,OAAO;QACL,KAAK,EAAE,SAAS;QAChB,YAAY,EAAE,OAAO;QACrB,gBAAgB,EAAE,KAAK;QACvB,gBAAgB,EAAE;YAChB,KAAK,EAAE,OAAO;SACf;QACD,eAAe,EAAE,OAAO;QACxB,qBAAqB,EAAE;YACrB,KAAK,EAAE,OAAO;SACf;QACD,WAAW,EAAE;YACX,eAAe,EAAE,OAAO;YACxB,6BAA6B;YAC7B,iBAAiB,EAAE,SAAS;SAC7B;QACD,MAAM,EAAE,GAAG,EAAE;YACX,MAAM,cAAc,GAAG,uBAAQ,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,6CAAY,CAAC,CAAC,CAAC,mBAAI,CAAC;YACvE,OAAO,CACL,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CACnC;UAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAChC;YAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAC7B;cAAA,CAAC,WAAW,CAAC,AAAD,EACd;YAAA,EAAE,mBAAI,CACN;YAAA,CAAC,mBAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACtD;;YACF,EAAE,mBAAI,CACR;UAAA,EAAE,mBAAI,CACR;QAAA,EAAE,cAAc,CAAC,CAClB,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAgB,OAAO;IACrB,OAAO,CACL,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAC5B;MAAA,CAAC,wCAA4B,IAAI,CAAC,wBAAS,CAAC,QAAQ,CAAC,eAAe,EAAG,CACvE;MAAA,CAAC,yBAAU,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAC/C;QAAA,CAAC,cAAc,CAAC,AAAD,EACjB;MAAA,EAAE,yBAAU,CACd;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,SAAS,cAAc;IACrB,qEAAqE;IACrE,IAAI,CAAC,oBAAK,CAAC,SAAS;QAAE,OAAO,IAAI,CAAC;IAElC,OAAO,oBAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAU,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAC9D,CAAC,mBAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CACvD;MAAA,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EACzB;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC,CAAC;AACL,CAAC;AAED,SAAS,QAAQ,CAAC,EAChB,KAAK,EACL,KAAK,GAAG,CAAC,EACT,OAAO,GAAG,EAAE,EACZ,SAAS,GAAG,KAAK,GAMlB;IACC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IAE3C,MAAM,QAAQ,GAAG,eAAK,CAAC,OAAO,CAC5B,GAAG,EAAE,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAC7C,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CACvB,CAAC;IAEF,MAAM,IAAI,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,OAAO,CACL,GAAG;YACH,QAAQ;iBACL,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;gBACf,mEAAmE;gBACnE,IAAI,IAAA,oCAAyB,EAAC,OAAO,CAAC,EAAE,CAAC;oBACvC,OAAO,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACpC,CAAC;gBACD,oDAAoD;gBACpD,OAAO,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5C,CAAC,CAAC;iBACD,MAAM,CAAC,OAAO,CAAC;iBACf,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAE5B,MAAM,QAAQ,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7C,2CAA2C;QAC3C,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAChD,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,MAAM,kBAAkB,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QAEzD,0CAA0C;QAC1C,oFAAoF;QACpF,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAEtE,OAAO,CACL,EACE;MAAA,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAI,CAClB,CAAC,WAAI,CACH,kBAAkB,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CACrC,IAAI,CAAC,CAAC,IAAI,CAAC,CACX,OAAO,CAAC,CAAC,GAAG,EAAE;gBACZ,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,uBAAM,CAAC,SAAS,EAAE,EAAE,CAAC;oBAChD,wBAAwB;oBACxB,uBAAM,CAAC,IAAI,EAAE,CAAC;gBAChB,CAAC;YACH,CAAC,CAAC,CACF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CACnB,OAAO;QACP,mEAAmE;QACnE,OAAO,CACP;UAAA,CAAC,qBAAS,CACR;YAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;oBACL,MAAM,CAAC,aAAa;oBACpB;wBACE,WAAW,EAAE,MAAM,GAAG,KAAK,GAAG,MAAM;wBACpC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa;qBACrD;oBACD,OAAO,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE;oBACzC,QAAQ,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE;iBAC7B,CAAC,CACF;gBAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAC1D;kBAAA,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,AAAD,EAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,AAAD,EAAG,CACnD;kBAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,mBAAI,CAChD;gBAAA,EAAE,mBAAI,CAEN;;gBAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAC1D;kBAAA,CAAC,CAAC,CAAC,IAAI,IAAI,CACT,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,QAAQ,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,mBAAI,CAAC,CAC9E,CACD;kBAAA,CAAC,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,AAAD,EAAG,CAC/B;gBAAA,EAAE,mBAAI,CACR;cAAA,EAAE,mBAAI,CAAC,CACR,CACH;UAAA,EAAE,qBAAS,CACb;QAAA,EAAE,WAAI,CAAC,CACR,CACD;MAAA,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAC7B,CAAC,QAAQ,CACP,GAAG,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CACtB,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,SAAS,CAAC,CAAC,KAAK,CAAC,gBAAgB,KAAK,KAAK,CAAC,KAAK,CAAC,CAClD,OAAO,CAAC,CAAC,QAAQ,CAAC,CAClB,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACzC,CACH,CAAC,CACJ;IAAA,GAAG,CACJ,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ;IACf,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC,EAAG,CAAC;AACxF,CAAC;AAED,SAAS,OAAO;IACd,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC,EAAG,CAAC;AACvF,CAAC;AAED,SAAS,WAAW;IAClB,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,EAAG,CAAC;AAC3F,CAAC;AAED,SAAS,WAAW;IAClB,OAAO,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,EAAG,CAAC;AAC3F,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE;QACT,eAAe,EAAE,OAAO;QACxB,IAAI,EAAE,CAAC;QACP,UAAU,EAAE,SAAS;KACtB;IACD,MAAM,EAAE;QACN,eAAe,EAAE,SAAS;QAC1B,eAAe,EAAE,EAAE;QACnB,iBAAiB,EAAE,CAAC;QACpB,WAAW,EAAE,SAAS;QACtB,WAAW,EAAE,MAAM;QACnB,YAAY,EAAE;YACZ,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,CAAC;SACV;QACD,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC;QACf,SAAS,EAAE,CAAC;KACb;IACD,aAAa,EAAE;QACb,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,QAAQ;QACpB,GAAG,EAAE,EAAE;QACP,iBAAiB,EAAE,IAAI;QACvB,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,GAAG;gBACb,gBAAgB,EAAE,MAAM;aACzB;SACF,CAAC;KACH;IACD,KAAK,EAAE;QACL,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,MAAM;KACnB;IACD,MAAM,EAAE;QACN,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,EAAE;QACnB,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,aAAa,EAAE,EAAE;aAClB;YACD,GAAG,EAAE;gBACH,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,GAAG;gBACb,gBAAgB,EAAE,MAAM;gBACxB,aAAa,EAAE,EAAE;aAClB;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,EAAE;aAClB;SACF,CAAC;KACH;IACD,aAAa,EAAE;QACb,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,SAAS;QACtB,eAAe,EAAE,SAAS;QAC1B,YAAY,EAAE,EAAE;QAChB,YAAY,EAAE,EAAE;QAChB,QAAQ,EAAE,QAAQ;KACnB;IACD,aAAa,EAAE;QACb,iBAAiB,EAAE,MAAM;QACzB,eAAe,EAAE,EAAE;QACnB,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,eAAe;QAC/B,UAAU,EAAE,QAAQ;QACpB,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,kBAAkB,EAAE,OAAO;aAC5B;SACF,CAAC;KACH;IACD,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;IAC/C,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE;IACrE,UAAU,EAAE;QACV,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,EAAE;QACV,eAAe,EAAE,SAAS;QAC1B,YAAY,EAAE,CAAC;QACf,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;KACzB;CACF,CAAC,CAAC", "sourcesContent": ["// Copyright © 2024 650 Industries.\n'use client';\n\nimport { NativeStackNavigationOptions } from '@react-navigation/native-stack';\nimport React from 'react';\nimport { Image, StyleSheet, Text, View, ScrollView, Platform, StatusBar } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\n\nimport { Pressable } from './Pressable';\nimport { RouteNode, sortRoutes } from '../Route';\nimport { store } from '../global-state/router-store';\nimport { router } from '../imperative-api';\nimport { Link } from '../link/Link';\nimport { matchDeepDynamicRouteName } from '../matchers';\nimport { canOverrideStatusBarBehavior } from '../utils/statusbar';\n\nconst INDENT = 20;\n\nexport function getNavOptions(): NativeStackNavigationOptions {\n  return {\n    title: 'sitemap',\n    presentation: 'modal',\n    headerLargeTitle: false,\n    headerTitleStyle: {\n      color: 'white',\n    },\n    headerTintColor: 'white',\n    headerLargeTitleStyle: {\n      color: 'white',\n    },\n    headerStyle: {\n      backgroundColor: 'black',\n      // @ts-expect-error: mistyped\n      borderBottomColor: '#323232',\n    },\n    header: () => {\n      const WrapperElement = Platform.OS === 'android' ? SafeAreaView : View;\n      return (\n        <WrapperElement style={styles.header}>\n          <View style={styles.headerContent}>\n            <View style={styles.headerIcon}>\n              <SitemapIcon />\n            </View>\n            <Text role=\"heading\" aria-level={1} style={styles.title}>\n              Sitemap\n            </Text>\n          </View>\n        </WrapperElement>\n      );\n    },\n  };\n}\n\nexport function Sitemap() {\n  return (\n    <View style={styles.container}>\n      {canOverrideStatusBarBehavior && <StatusBar barStyle=\"light-content\" />}\n      <ScrollView contentContainerStyle={styles.scroll}>\n        <FileSystemView />\n      </ScrollView>\n    </View>\n  );\n}\n\nfunction FileSystemView() {\n  // This shouldn't occur, as the user should be on the tutorial screen\n  if (!store.routeNode) return null;\n\n  return store.routeNode.children.sort(sortRoutes).map((route) => (\n    <View key={route.contextKey} style={styles.itemContainer}>\n      <FileItem route={route} />\n    </View>\n  ));\n}\n\nfunction FileItem({\n  route,\n  level = 0,\n  parents = [],\n  isInitial = false,\n}: {\n  route: RouteNode;\n  level?: number;\n  parents?: string[];\n  isInitial?: boolean;\n}) {\n  const disabled = route.children.length > 0;\n\n  const segments = React.useMemo(\n    () => [...parents, ...route.route.split('/')],\n    [parents, route.route]\n  );\n\n  const href = React.useMemo(() => {\n    return (\n      '/' +\n      segments\n        .map((segment) => {\n          // add an extra layer of entropy to the url for deep dynamic routes\n          if (matchDeepDynamicRouteName(segment)) {\n            return segment + '/' + Date.now();\n          }\n          // index must be erased but groups can be preserved.\n          return segment === 'index' ? '' : segment;\n        })\n        .filter(Boolean)\n        .join('/')\n    );\n  }, [segments, route.route]);\n\n  const filename = React.useMemo(() => {\n    const segments = route.contextKey.split('/');\n    // join last two segments for layout routes\n    if (route.contextKey.match(/_layout\\.[jt]sx?$/)) {\n      return segments[segments.length - 2] + '/' + segments[segments.length - 1];\n    }\n\n    const routeSegmentsCount = route.route.split('/').length;\n\n    // Join the segment count in reverse order\n    // This presents files without layout routes as children with all relevant segments.\n    return segments.slice(-routeSegmentsCount).join('/');\n  }, [route]);\n\n  const info = isInitial ? 'Initial' : route.generated ? 'Virtual' : '';\n\n  return (\n    <>\n      {!route.internal && (\n        <Link\n          accessibilityLabel={route.contextKey}\n          href={href}\n          onPress={() => {\n            if (Platform.OS !== 'web' && router.canGoBack()) {\n              // Ensure the modal pops\n              router.back();\n            }\n          }}\n          disabled={disabled}\n          asChild\n          // Ensure we replace the history so you can't go back to this page.\n          replace>\n          <Pressable>\n            {({ pressed, hovered }) => (\n              <View\n                style={[\n                  styles.itemPressable,\n                  {\n                    paddingLeft: INDENT + level * INDENT,\n                    backgroundColor: hovered ? '#202425' : 'transparent',\n                  },\n                  pressed && { backgroundColor: '#26292b' },\n                  disabled && { opacity: 0.4 },\n                ]}>\n                <View style={{ flexDirection: 'row', alignItems: 'center' }}>\n                  {route.children.length ? <PkgIcon /> : <FileIcon />}\n                  <Text style={styles.filename}>{filename}</Text>\n                </View>\n\n                <View style={{ flexDirection: 'row', alignItems: 'center' }}>\n                  {!!info && (\n                    <Text style={[styles.virtual, !disabled && { marginRight: 8 }]}>{info}</Text>\n                  )}\n                  {!disabled && <ForwardIcon />}\n                </View>\n              </View>\n            )}\n          </Pressable>\n        </Link>\n      )}\n      {route.children.map((child) => (\n        <FileItem\n          key={child.contextKey}\n          route={child}\n          isInitial={route.initialRouteName === child.route}\n          parents={segments}\n          level={level + (route.generated ? 0 : 1)}\n        />\n      ))}\n    </>\n  );\n}\n\nfunction FileIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/file.png')} />;\n}\n\nfunction PkgIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/pkg.png')} />;\n}\n\nfunction ForwardIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/forward.png')} />;\n}\n\nfunction SitemapIcon() {\n  return <Image style={styles.image} source={require('expo-router/assets/sitemap.png')} />;\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    backgroundColor: 'black',\n    flex: 1,\n    alignItems: 'stretch',\n  },\n  header: {\n    backgroundColor: '#151718',\n    paddingVertical: 16,\n    borderBottomWidth: 1,\n    borderColor: '#313538',\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 3,\n    },\n    shadowOpacity: 0.33,\n    shadowRadius: 3,\n    elevation: 8,\n  },\n  headerContent: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    gap: 14,\n    paddingHorizontal: '5%',\n    ...Platform.select({\n      web: {\n        width: '100%',\n        maxWidth: 960,\n        marginHorizontal: 'auto',\n      },\n    }),\n  },\n  title: {\n    color: 'white',\n    fontSize: 28,\n    fontWeight: 'bold',\n  },\n  scroll: {\n    paddingHorizontal: '5%',\n    paddingVertical: 16,\n    ...Platform.select({\n      ios: {\n        paddingBottom: 24,\n      },\n      web: {\n        width: '100%',\n        maxWidth: 960,\n        marginHorizontal: 'auto',\n        paddingBottom: 24,\n      },\n      default: {\n        paddingBottom: 12,\n      },\n    }),\n  },\n  itemContainer: {\n    borderWidth: 1,\n    borderColor: '#313538',\n    backgroundColor: '#151718',\n    borderRadius: 12,\n    marginBottom: 12,\n    overflow: 'hidden',\n  },\n  itemPressable: {\n    paddingHorizontal: INDENT,\n    paddingVertical: 16,\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    ...Platform.select({\n      web: {\n        transitionDuration: '100ms',\n      },\n    }),\n  },\n  filename: { color: 'white', fontSize: 20, marginLeft: 12 },\n  virtual: { textAlign: 'right', color: 'white' },\n  image: { width: 24, height: 24, resizeMode: 'contain', opacity: 0.6 },\n  headerIcon: {\n    width: 40,\n    height: 40,\n    backgroundColor: '#202425',\n    borderRadius: 8,\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n});\n"]}