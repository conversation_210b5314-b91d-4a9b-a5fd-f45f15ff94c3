{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/reactNativeConfig/config.ts"], "names": [], "mappings": ";;;;;AAeA,0CAmCC;AAlDD,2DAA6B;AAC7B,gDAAwB;AACxB,8EAAoD;AACpD,gEAAuC;AAGvC,4CAA+C;AAE/C,IAAI,MAAM,GAAmD,SAAS,CAAC;AAEvE,MAAM,mBAAmB,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;AAElF;;GAEG;AACI,KAAK,UAAU,eAAe,CACnC,WAAmB;IAEnB,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;IACtE,IAAI,MAAM,IAAA,2BAAe,EAAC,YAAY,CAAC,EAAE,CAAC;QACxC,OAAO,aAAa,CAAC,YAAY,EAAE,MAAM,kBAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;IACtE,IAAI,MAAM,IAAA,2BAAe,EAAC,YAAY,CAAC,EAAE,CAAC;QACxC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAC7D,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAC/D,MAAM,kBAAkB,GAAG,MAAM,EAAE,eAAe,CAAC,cAAc,EAAE;YACjE,eAAe,EAAE;gBACf,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ;gBAClC,gBAAgB,EAAE,MAAM,CAAC,oBAAoB,CAAC,QAAQ;gBACtD,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,MAAM;aACnC;SACF,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,kBAAkB,EAAE,UAAU,CAAC;QAElD,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,aAAa,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,SAAS,aAAa,CAAC,QAAgB,EAAE,cAAsB;IAC7D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAA,6BAAiB,EAAC,cAAc,EAAE,QAAQ,EAAE;YACzD,YAAY,EAAE,CAAC,mBAAmB,CAAC;SACpC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,OAAO,IAAI,MAAM,IAAI,IAAI,CAAC;IAC1C,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["import fs from 'fs/promises';\nimport path from 'path';\nimport requireFromString from 'require-from-string';\nimport resolveFrom from 'resolve-from';\n\nimport type { RNConfigReactNativeConfig } from './reactNativeConfig.types';\nimport { fileExistsAsync } from '../fileUtils';\n\nlet tsMain: typeof import('typescript') | null | undefined = undefined;\n\nconst mockedNativeModules = path.join(__dirname, '..', '..', 'node_modules_mock');\n\n/**\n * Load the `react-native.config.js` or `react-native.config.ts` from the package.\n */\nexport async function loadConfigAsync<T extends RNConfigReactNativeConfig>(\n  packageRoot: string\n): Promise<T | null> {\n  const configJsPath = path.join(packageRoot, 'react-native.config.js');\n  if (await fileExistsAsync(configJsPath)) {\n    return requireConfig(configJsPath, await fs.readFile(configJsPath, 'utf8'));\n  }\n\n  const configTsPath = path.join(packageRoot, 'react-native.config.ts');\n  if (await fileExistsAsync(configTsPath)) {\n    if (tsMain === undefined) {\n      const tsPath = resolveFrom.silent(packageRoot, 'typescript');\n      if (tsPath) {\n        tsMain = require(tsPath);\n      }\n    } else if (tsMain == null) {\n      return null;\n    }\n\n    const configContents = await fs.readFile(configTsPath, 'utf8');\n    const transpiledContents = tsMain?.transpileModule(configContents, {\n      compilerOptions: {\n        module: tsMain.ModuleKind.NodeNext,\n        moduleResolution: tsMain.ModuleResolutionKind.NodeNext,\n        target: tsMain.ScriptTarget.ESNext,\n      },\n    });\n    const outputText = transpiledContents?.outputText;\n\n    if (outputText) {\n      return requireConfig(configTsPath, outputText);\n    }\n  }\n\n  return null;\n}\n\n/**\n * Temporarily, we need to mock the community CLI, because\n * some packages are checking the version of the CLI in the `react-native.config.js` file.\n * We can remove this once we remove this check from packages.\n */\nfunction requireConfig(filepath: string, configContents: string) {\n  try {\n    const config = requireFromString(configContents, filepath, {\n      prependPaths: [mockedNativeModules],\n    });\n    return config.default ?? config ?? null;\n  } catch {\n    return null;\n  }\n}\n"]}