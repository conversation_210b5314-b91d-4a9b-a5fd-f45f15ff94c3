{"version": 3, "file": "Strings.js", "names": ["_Paths", "data", "require", "getProjectStringsXMLPathAsync", "projectRoot", "kind", "getResourceXMLPathAsync", "name", "setStringItem", "itemToAdd", "stringFileContentsJSON", "resources", "string", "newItem", "stringNameExists", "findIndex", "e", "$", "concat", "removeStringItem", "named", "splice"], "sources": ["../../src/android/Strings.ts"], "sourcesContent": ["import { getResourceXMLPathAsync } from './Paths';\nimport { ResourceItemXML, ResourceKind, ResourceXML } from './Resources';\n\nexport async function getProjectStringsXMLPathAsync(\n  projectRoot: string,\n  { kind }: { kind?: ResourceKind } = {}\n): Promise<string> {\n  return getResourceXMLPathAsync(projectRoot, { kind, name: 'strings' });\n}\n\nexport function setStringItem(\n  itemToAdd: ResourceItemXML[],\n  stringFileContentsJSON: ResourceXML\n): ResourceXML {\n  if (!stringFileContentsJSON?.resources?.string) {\n    if (!stringFileContentsJSON.resources || typeof stringFileContentsJSON.resources === 'string') {\n      // file was empty and JSON is `{resources : ''}`\n      stringFileContentsJSON.resources = {};\n    }\n    stringFileContentsJSON.resources.string = itemToAdd;\n    return stringFileContentsJSON;\n  }\n\n  for (const newItem of itemToAdd) {\n    const stringNameExists = stringFileContentsJSON.resources.string.findIndex(\n      (e: ResourceItemXML) => e.$.name === newItem.$.name\n    );\n    if (stringNameExists > -1) {\n      // replace the previous item\n      stringFileContentsJSON.resources.string[stringNameExists] = newItem;\n    } else {\n      stringFileContentsJSON.resources.string =\n        stringFileContentsJSON.resources.string.concat(newItem);\n    }\n  }\n  return stringFileContentsJSON;\n}\n\nexport function removeStringItem(named: string, stringFileContentsJSON: ResourceXML): ResourceXML {\n  if (stringFileContentsJSON?.resources?.string) {\n    const stringNameExists = stringFileContentsJSON.resources.string.findIndex(\n      (e: ResourceItemXML) => e.$.name === named\n    );\n    if (stringNameExists > -1) {\n      // replace the previous value\n      stringFileContentsJSON.resources.string.splice(stringNameExists, 1);\n    }\n  }\n  return stringFileContentsJSON;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,eAAeE,6BAA6BA,CACjDC,WAAmB,EACnB;EAAEC;AAA8B,CAAC,GAAG,CAAC,CAAC,EACrB;EACjB,OAAO,IAAAC,gCAAuB,EAACF,WAAW,EAAE;IAAEC,IAAI;IAAEE,IAAI,EAAE;EAAU,CAAC,CAAC;AACxE;AAEO,SAASC,aAAaA,CAC3BC,SAA4B,EAC5BC,sBAAmC,EACtB;EACb,IAAI,CAACA,sBAAsB,EAAEC,SAAS,EAAEC,MAAM,EAAE;IAC9C,IAAI,CAACF,sBAAsB,CAACC,SAAS,IAAI,OAAOD,sBAAsB,CAACC,SAAS,KAAK,QAAQ,EAAE;MAC7F;MACAD,sBAAsB,CAACC,SAAS,GAAG,CAAC,CAAC;IACvC;IACAD,sBAAsB,CAACC,SAAS,CAACC,MAAM,GAAGH,SAAS;IACnD,OAAOC,sBAAsB;EAC/B;EAEA,KAAK,MAAMG,OAAO,IAAIJ,SAAS,EAAE;IAC/B,MAAMK,gBAAgB,GAAGJ,sBAAsB,CAACC,SAAS,CAACC,MAAM,CAACG,SAAS,CACvEC,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACV,IAAI,KAAKM,OAAO,CAACI,CAAC,CAACV,IACjD,CAAC;IACD,IAAIO,gBAAgB,GAAG,CAAC,CAAC,EAAE;MACzB;MACAJ,sBAAsB,CAACC,SAAS,CAACC,MAAM,CAACE,gBAAgB,CAAC,GAAGD,OAAO;IACrE,CAAC,MAAM;MACLH,sBAAsB,CAACC,SAAS,CAACC,MAAM,GACrCF,sBAAsB,CAACC,SAAS,CAACC,MAAM,CAACM,MAAM,CAACL,OAAO,CAAC;IAC3D;EACF;EACA,OAAOH,sBAAsB;AAC/B;AAEO,SAASS,gBAAgBA,CAACC,KAAa,EAAEV,sBAAmC,EAAe;EAChG,IAAIA,sBAAsB,EAAEC,SAAS,EAAEC,MAAM,EAAE;IAC7C,MAAME,gBAAgB,GAAGJ,sBAAsB,CAACC,SAAS,CAACC,MAAM,CAACG,SAAS,CACvEC,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACV,IAAI,KAAKa,KACvC,CAAC;IACD,IAAIN,gBAAgB,GAAG,CAAC,CAAC,EAAE;MACzB;MACAJ,sBAAsB,CAACC,SAAS,CAACC,MAAM,CAACS,MAAM,CAACP,gBAAgB,EAAE,CAAC,CAAC;IACrE;EACF;EACA,OAAOJ,sBAAsB;AAC/B", "ignoreList": []}