const set = require('regenerate')(0x3037, 0xFF70, 0x1B132, 0x1F200);
set.addRange(0x3001, 0x3003).addRange(0x3008, 0x3011).addRange(0x3013, 0x301F).addRange(0x3030, 0x3035).addRange(0x303C, 0x303D).addRange(0x3041, 0x3096).addRange(0x3099, 0x30A0).addRange(0x30FB, 0x30FC).addRange(0xFE45, 0xFE46).addRange(0xFF61, 0xFF65).addRange(0xFF9E, 0xFF9F).addRange(0x1B001, 0x1B11F).addRange(0x1B150, 0x1B152);
exports.characters = set;
