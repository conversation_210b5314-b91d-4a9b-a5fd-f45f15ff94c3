{"version": 3, "file": "serverLocationContext.js", "sourceRoot": "", "sources": ["../../src/global-state/serverLocationContext.ts"], "names": [], "mappings": ";;;AAAA,iIAAiI;AACjI,iCAAsC;AASzB,QAAA,aAAa,GAAG,IAAA,qBAAa,EAAgC,SAAS,CAAC,CAAC", "sourcesContent": ["// This is file should mirror https://github.com/react-navigation/react-navigation/blob/6.x/packages/native/src/ServerContext.tsx\nimport { createContext } from 'react';\n\nexport type ServerContextType = {\n  location?: {\n    pathname: string;\n    search: string;\n  };\n};\n\nexport const ServerContext = createContext<ServerContextType | undefined>(undefined);\n"]}