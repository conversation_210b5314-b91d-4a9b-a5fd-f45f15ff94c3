<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="assets/style.css">
    <title>Hammer.js</title>
    <style>

        #right,
        #left {
            display: block;
            width: 50%;
            height: 500px;
            overflow: hidden;
        }

        #left { float: left; }
        #right { float: right; }

    </style>
</head>
<body>
    <div class="container">
        <pre id="left" class="bg1"></pre>
        <pre id="right" class="bg5"></pre>

        <div class="clear"></div>

        <h1>Multiple instances the same time</h1>
        <p>You can run multiple instances of Hammer on your page and they will recognize each completely isolated
            from each other. This makes it possible to build multi-user interfaces.</p>

    </div>
    <script src="../../dist/hammer.min.js"></script>
    <script>

        Object.prototype.toDirString = function() {
            var output = [];
            for(var key in this) {
                if(this.hasOwnProperty(key)) {
                    var value = this[key];
                    if(Array.isArray(value)) {
                        value = "Array("+ value.length +"):"+ value;
                    } else if(value instanceof HTMLElement) {
                        value = value +" ("+ value.outerHTML.substring(0, 50) +"...)";
                    }
                    output.push(key +": "+ value);
                }
            }
            return output.join("\n")
        };


        function addHammer(el) {
            var mc = new Hammer(el, { multiUser: true });
            mc.get('pan').set({ direction: Hammer.DIRECTION_ALL });
            mc.get('swipe').set({ direction: Hammer.DIRECTION_ALL });
            mc.get('pinch').set({ enable: true });
            mc.get('rotate').set({ enable: true });

            mc.on("swipe pan press pinch rotate tap doubletap", function (ev) {
                ev.preventDefault();
                el.innerText = ev.toDirString();
            });
        }

        addHammer(document.querySelector("#left"));
        addHammer(document.querySelector("#right"));

    </script>
</body>
</html>
