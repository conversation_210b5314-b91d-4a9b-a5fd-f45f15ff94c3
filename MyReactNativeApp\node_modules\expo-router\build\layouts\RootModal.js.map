{"version": 3, "file": "RootModal.js", "sourceRoot": "", "sources": ["../../src/layouts/RootModal.tsx"], "names": [], "mappings": ";;;AAgBA,8CAeC;AA/BD,iCAAqD;AASxC,QAAA,gBAAgB,GAAG,IAAA,qBAAa,EAAwB;IACnE,IAAI,EAAE,IAAI;IACV,MAAM,EAAE,EAAE;IACV,QAAQ,EAAE,GAAG,EAAE,GAAE,CAAC;IAClB,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;CACtB,CAAC,CAAC;AAEH,SAAgB,iBAAiB,CAAC,EAAE,QAAQ,EAAiC;IAC3E,MAAM,MAAM,GAAG,IAAA,WAAG,EAAC,wBAAgB,CAAC,CAAC;IAErC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAwB,GAAG,EAAE,CAAC,CAAC;QAC/D,IAAI,EAAE,KAAK;QACX,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,CAAC,IAAY,EAAE,EAAE;YACzB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnF,CAAC;QACD,WAAW,EAAE,CAAC,IAAY,EAAE,EAAE;YAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnF,CAAC;KACF,CAAC,CAAC,CAAC;IAEJ,OAAO,CAAC,wBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,wBAAgB,CAAC,QAAQ,CAAC,CAAC;AACzF,CAAC", "sourcesContent": ["import { createContext, use, useState } from 'react';\n\ntype RootModalContextValue = {\n  root: boolean;\n  routes: never[];\n  addModal: (name: string) => void;\n  removeModal: (name: string) => void;\n};\n\nexport const RootModalContext = createContext<RootModalContextValue>({\n  root: true,\n  routes: [],\n  addModal: () => {},\n  removeModal: () => {},\n});\n\nexport function RootModalProvider({ children }: { children: React.ReactNode }) {\n  const parent = use(RootModalContext);\n\n  const [state, setState] = useState<RootModalContextValue>(() => ({\n    root: false,\n    routes: [],\n    addModal: (name: string) => {\n      return parent.root ? setState((state) => ({ ...state })) : parent.addModal(name);\n    },\n    removeModal: (name: string) => {\n      return parent.root ? setState((state) => ({ ...state })) : parent.addModal(name);\n    },\n  }));\n\n  return <RootModalContext.Provider value={state}>{children}</RootModalContext.Provider>;\n}\n"]}