{"version": 3, "file": "Slot.js", "sourceRoot": "", "sources": ["../../src/ui/Slot.tsx"], "names": [], "mappings": ";;;AAAA,qDAAuD;AACvD,iCAA4C;AAC5C,+CAA0C;AAE1C;;;;;;;;;;;;GAYG;AACH,SAAS,sBAAsB,CAAC,SAAyB;IACvD,OAAO,IAAA,kBAAU,EAAC,SAAS,SAAS,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG;QAC3D,KAAK,GAAG,IAAA,eAAO,EAAC,GAAG,EAAE,CAAC,yBAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1D,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAG,CAAC;IAC1D,CAAC,CAAC,CAAC;AACL,CAAC;AAEY,QAAA,IAAI,GAAG,sBAAsB,CAAC,iBAAO,CAAC,CAAC", "sourcesContent": ["import { Slot as RUISlot } from '@radix-ui/react-slot';\nimport { forwardRef, useMemo } from 'react';\nimport { StyleSheet } from 'react-native';\n\n/**\n * RadixUI has special logic to handle the merging of `style` and `className` props.\n * On the web styles are not allowed so <PERSON><PERSON><PERSON> does not handle this scenario.\n * This could be fixed upstream (PR open), but it may not as RN is not their target\n * platform.\n *\n * This shim calls `StyleSheet.flatten` on the styles before we render the <Slot />\n *\n * @see https://github.com/expo/expo/issues/31352\n * @see https://github.com/radix-ui/primitives/issues/3107\n * @param Component\n * @returns\n */\nfunction ShimSlotForReactNative(Component: typeof RUISlot): typeof RUISlot {\n  return forwardRef(function RNSlotHOC({ style, ...props }, ref) {\n    style = useMemo(() => StyleSheet.flatten(style), [style]);\n    return <Component ref={ref} {...props} style={style} />;\n  });\n}\n\nexport const Slot = ShimSlotForReactNative(RUISlot);\n"]}