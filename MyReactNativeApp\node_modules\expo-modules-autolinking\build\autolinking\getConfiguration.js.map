{"version": 3, "file": "getConfiguration.js", "sourceRoot": "", "sources": ["../../src/autolinking/getConfiguration.ts"], "names": [], "mappings": ";;AAGA,4CAGC;AALD,mCAA8D;AAE9D,SAAgB,gBAAgB,CAAC,OAAuB;IACtD,MAAM,eAAe,GAAG,IAAA,2CAAmC,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9E,OAAO,eAAe,CAAC,gBAAgB,EAAE,CAAC,OAAO,CAAC,CAAC;AACrD,CAAC", "sourcesContent": ["import type { ResolveOptions } from '../types';\nimport { getLinkingImplementationForPlatform } from './utils';\n\nexport function getConfiguration(options: ResolveOptions) {\n  const platformLinking = getLinkingImplementationForPlatform(options.platform);\n  return platformLinking.getConfiguration?.(options);\n}\n"]}