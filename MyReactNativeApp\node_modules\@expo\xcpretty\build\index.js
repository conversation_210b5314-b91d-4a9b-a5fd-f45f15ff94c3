"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Runner = exports.switchRegex = exports.ExpoRunFormatter = exports.PodfileTracer = exports.MetroParser = exports.Parser = void 0;
const Runner = __importStar(require("./Runner"));
exports.Runner = Runner;
var Parser_1 = require("./Parser");
Object.defineProperty(exports, "Parser", { enumerable: true, get: function () { return Parser_1.Parser; } });
var MetroParser_1 = require("./MetroParser");
Object.defineProperty(exports, "MetroParser", { enumerable: true, get: function () { return MetroParser_1.MetroParser; } });
var PodfileTracer_1 = require("./utils/PodfileTracer");
Object.defineProperty(exports, "PodfileTracer", { enumerable: true, get: function () { return PodfileTracer_1.PodfileTracer; } });
var ExpoRunFormatter_1 = require("./ExpoRunFormatter");
Object.defineProperty(exports, "ExpoRunFormatter", { enumerable: true, get: function () { return ExpoRunFormatter_1.ExpoRunFormatter; } });
__exportStar(require("./Formatter"), exports);
var switchRegex_1 = require("./switchRegex");
Object.defineProperty(exports, "switchRegex", { enumerable: true, get: function () { return switchRegex_1.switchRegex; } });
//# sourceMappingURL=index.js.map