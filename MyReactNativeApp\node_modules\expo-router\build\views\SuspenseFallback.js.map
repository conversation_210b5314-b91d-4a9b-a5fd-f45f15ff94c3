{"version": 3, "file": "SuspenseFallback.js", "sourceRoot": "", "sources": ["../../src/views/SuspenseFallback.tsx"], "names": [], "mappings": ";;;;;AAKA,4CAUC;AAfD,kDAA0B;AAE1B,mCAA8C;AAG9C,SAAgB,gBAAgB,CAAC,EAAE,KAAK,EAAwB;IAC9D,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CACL,CAAC,oBAAY,CACX;QAAA,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,WAAW,EAAE,aAAK,CACxD;MAAA,EAAE,oBAAY,CAAC,CAChB,CAAC;IACJ,CAAC;IACD,iDAAiD;IACjD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import React from 'react';\n\nimport { Toast, ToastWrapper } from './Toast';\nimport { RouteNode } from '../Route';\n\nexport function SuspenseFallback({ route }: { route: RouteNode }) {\n  if (__DEV__) {\n    return (\n      <ToastWrapper>\n        <Toast filename={route?.contextKey}>Bundling...</Toast>\n      </ToastWrapper>\n    );\n  }\n  // TODO: Support user's customizing the fallback.\n  return null;\n}\n"]}