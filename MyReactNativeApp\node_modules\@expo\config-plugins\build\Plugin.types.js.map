{"version": 3, "file": "Plugin.types.js", "names": ["_xcode", "data", "require", "_Manifest", "_IosConfig"], "sources": ["../src/Plugin.types.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport { JSONObject } from '@expo/json-file';\nimport { XcodeProject } from 'xcode';\n\nimport { Properties } from './android';\nimport { AndroidManifest } from './android/Manifest';\nimport * as AndroidPaths from './android/Paths';\nimport { ResourceXML } from './android/Resources';\nimport { ExpoPlist, InfoPlist } from './ios/IosConfig.types';\nimport { AppDelegateProjectFile } from './ios/Paths';\n\ntype OptionalPromise<T> = Promise<T> | T;\n\ntype Plist = JSONObject;\n\nexport interface ModProps<T = any> {\n  /**\n   * Project root directory for the universal app.\n   */\n  readonly projectRoot: string;\n\n  /**\n   * Project root for the specific platform.\n   */\n  readonly platformProjectRoot: string;\n\n  /**\n   * Name of the mod.\n   */\n  readonly modName: string;\n\n  /**\n   * Name of the platform used in the mods config.\n   */\n  readonly platform: ModPlatform;\n\n  /**\n   * If the mod is being evaluated in introspection mode.\n   * No file system modifications should be made when introspect is `true`.\n   */\n  readonly introspect: boolean;\n\n  /**\n   * [iOS]: The path component used for querying project files.\n   *\n   * @example projectRoot/ios/[projectName]/\n   */\n  readonly projectName?: string;\n\n  /**\n   * Ignore any of the user's local native files and solely rely on the generated files.\n   * This makes prebuild data, like entitlements, more aligned to what users expects.\n   * When enabling this, users must be informed and have a way to disable this exclusion.\n   */\n  readonly ignoreExistingNativeFiles?: boolean;\n\n  nextMod?: Mod<T>;\n}\n\n// TODO: Migrate ProjectConfig to using expo instead if exp\nexport interface ExportedConfig extends ExpoConfig {\n  mods?: ModConfig | null;\n}\n\nexport interface ExportedConfigWithProps<Data = any> extends ExportedConfig {\n  /**\n   * The Object representation of a complex file type.\n   */\n  modResults: Data;\n  modRequest: ModProps<Data>;\n  /**\n   * A frozen representation of the original file contents,\n   * this can be used as a reference into the user's original intent.\n   *\n   * For example, you could infer that the user defined a certain\n   * value explicitly and disable any automatic changes.\n   */\n  readonly modRawConfig: ExpoConfig;\n}\n\n/**\n * A helper type to get the properties of a plugin.\n */\nexport type PluginParameters<T extends ConfigPlugin<any>> = T extends (\n  config: any,\n  props: infer P\n) => any\n  ? P\n  : never;\n\nexport type ConfigPlugin<Props = void> = (config: ExpoConfig, props: Props) => ExpoConfig;\n\nexport type StaticPlugin<T = any> = [string | ConfigPlugin<T>, T];\n\nexport type Mod<Props = any> = ((\n  config: ExportedConfigWithProps<Props>\n) => OptionalPromise<ExportedConfigWithProps<Props>>) & {\n  /**\n   * Indicates that the mod provides data upstream to other mods.\n   * This mod should always be the last one added.\n   */\n  isProvider?: boolean;\n  /**\n   * If the mod supports introspection, and avoids making any filesystem modifications during compilation.\n   * By enabling, this mod, and all of its descendants will be run in introspection mode.\n   * This should only be used for static files like JSON or XML, and not for application files that require regexes,\n   * or complex static files that require other files to be generated like Xcode `.pbxproj`.\n   */\n  isIntrospective?: boolean;\n};\n\nexport interface ModConfig {\n  android?: {\n    /**\n     * Dangerously make a modification before any other platform mods have been run.\n     */\n    dangerous?: Mod<unknown>;\n    /**\n     * Dangerously make a modification after all the other platform mods have been run.\n     */\n    finalized?: Mod<unknown>;\n    /**\n     * Modify the `android/app/src/main/AndroidManifest.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n     */\n    manifest?: Mod<AndroidManifest>;\n    /**\n     * Modify the `android/app/src/main/res/values/strings.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n     */\n    strings?: Mod<ResourceXML>;\n    /**\n     * Modify the `android/app/src/main/res/values/colors.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n     */\n    colors?: Mod<ResourceXML>;\n    /**\n     * Modify the `android/app/src/main/res/values-night/colors.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n     */\n    colorsNight?: Mod<ResourceXML>;\n    /**\n     * Modify the `android/app/src/main/res/values/styles.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n     */\n    styles?: Mod<ResourceXML>;\n    /**\n     * Modify the `android/app/src/main/<package>/MainActivity.java` as a string.\n     */\n    mainActivity?: Mod<AndroidPaths.ApplicationProjectFile>;\n    /**\n     * Modify the `android/app/src/main/<package>/MainApplication.java` as a string.\n     */\n    mainApplication?: Mod<AndroidPaths.ApplicationProjectFile>;\n    /**\n     * Modify the `android/app/build.gradle` as a string.\n     */\n    appBuildGradle?: Mod<AndroidPaths.GradleProjectFile>;\n    /**\n     * Modify the `android/build.gradle` as a string.\n     */\n    projectBuildGradle?: Mod<AndroidPaths.GradleProjectFile>;\n    /**\n     * Modify the `android/settings.gradle` as a string.\n     */\n    settingsGradle?: Mod<AndroidPaths.GradleProjectFile>;\n    /**\n     * Modify the `android/gradle.properties` as a `Properties.PropertiesItem[]`.\n     */\n    gradleProperties?: Mod<Properties.PropertiesItem[]>;\n  };\n  ios?: {\n    /**\n     * Dangerously make a modification before any other platform mods have been run.\n     */\n    dangerous?: Mod<unknown>;\n    /**\n     * Dangerously make a modification after all the other platform mods have been run.\n     */\n    finalized?: Mod<unknown>;\n    /**\n     * Modify the `ios/<name>/Info.plist` as JSON (parsed with [`@expo/plist`](https://www.npmjs.com/package/@expo/plist)).\n     */\n    infoPlist?: Mod<InfoPlist>;\n    /**\n     * Modify the `ios/<name>/<product-name>.entitlements` as JSON (parsed with [`@expo/plist`](https://www.npmjs.com/package/@expo/plist)).\n     */\n    entitlements?: Mod<Plist>;\n    /**\n     * Modify the `ios/<name>/Expo.plist` as JSON (Expo updates config for iOS) (parsed with [`@expo/plist`](https://www.npmjs.com/package/@expo/plist)).\n     */\n    expoPlist?: Mod<Plist>;\n    /**\n     * Modify the `ios/<name>.xcodeproj` as an `XcodeProject` (parsed with [`xcode`](https://www.npmjs.com/package/xcode))\n     */\n    xcodeproj?: Mod<XcodeProject>;\n    /**\n     * Modify the `ios/<name>/AppDelegate.m` as a string (dangerous)\n     */\n    appDelegate?: Mod<AppDelegateProjectFile>;\n    /**\n     * Modify the `ios/Podfile.properties.json` as key-value pairs\n     */\n    podfileProperties?: Mod<Record<string, string>>;\n  };\n}\n\nexport type ModPlatform = keyof ModConfig;\n\nexport { XcodeProject, InfoPlist, ExpoPlist, AndroidManifest };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA", "ignoreList": []}