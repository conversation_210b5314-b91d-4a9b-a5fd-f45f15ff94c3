{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/getDevServer/index.ts"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY,GAAG,GAAG,EAAE;IAC/B,kBAAkB;IAClB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO;YACL,sBAAsB,EAAE,IAAI;YAC5B,aAAa,EAAE,EAAE;YACjB,GAAG,EAAE,EAAE;SACR,CAAC;IACJ,CAAC;IAED,OAAO;QACL,4DAA4D;QAC5D,sBAAsB,EAAE,IAAI;QAE5B,0DAA0D;QAC1D,IAAI,aAAa;YACf,IAAI,QAAQ,EAAE,aAAa,IAAI,KAAK,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;gBAC/D,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;YACpC,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEzC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAE9C,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC9B,CAAC;QACD,GAAG,EAAE,QAAQ,CAAC,MAAM,GAAG,GAAG;KAC3B,CAAC;AACJ,CAAC,CAAC;AA5BW,QAAA,YAAY,gBA4BvB", "sourcesContent": ["export const getDevServer = () => {\n  // Disable for SSR\n  if (typeof window === 'undefined') {\n    return {\n      bundleLoadedFromServer: true,\n      fullBundleUrl: '',\n      url: '',\n    };\n  }\n\n  return {\n    // The bundle is always loaded from a server in the browser.\n    bundleLoadedFromServer: true,\n\n    /** URL but ensures that platform query param is added. */\n    get fullBundleUrl() {\n      if (document?.currentScript && 'src' in document.currentScript) {\n        return document.currentScript.src;\n      }\n\n      const bundleUrl = new URL(location.href);\n\n      bundleUrl.searchParams.set('platform', 'web');\n\n      return bundleUrl.toString();\n    },\n    url: location.origin + '/',\n  };\n};\n"]}