{"version": 3, "file": "renderStaticContent.js", "sourceRoot": "", "sources": ["../../src/static/renderStaticContent.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,4CAoDC;AAtFD;;;;;GAKG;AACH,+BAA6B;AAE7B,qDAA+E;AAC/E,6DAA+C;AAC/C,kDAA0B;AAC1B,wEAAmD;AACnD,wFAAwF;AACxF,uDAA+C;AAE/C,yDAAsD;AACtD,qCAAiC;AACjC,0CAAuC;AACvC,kCAA+B;AAE/B,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC,CAAC;AAElE,8BAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,mBAAQ,CAAC,CAAC;AAErD,SAAS,4BAA4B;IACnC,iDAAiD;IACjD,0JAA0J;IAE1J,8FAA8F;IAC9F,yJAAyJ;IACzJ,MAAM,QAAQ,GAAG,uCAAuC,CAAC;IACzD,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,EAA8B,CAAC;AAC3D,CAAC;AAEM,KAAK,UAAU,gBAAgB,CAAC,QAAa;IAClD,MAAM,WAAW,GAAqB,EAAE,CAAC;IAEzC,MAAM,GAAG,GAAG,eAAK,CAAC,SAAS,EAAsB,CAAC;IAElD,MAAM;IACJ,+DAA+D;IAC/D,kDAAkD;IAClD,OAAO,EACP,eAAe,GAChB,GAAG,8BAAW,CAAC,cAAc,CAAC,KAAK,EAAE;QACpC,YAAY,EAAE;YACZ,QAAQ;YACR,OAAO,EAAE,UAAG;YACZ,OAAO,EAAE,CAAC,EAAE,QAAQ,EAA6B,EAAE,EAAE,CAAC,CACpD,CAAC,IAAI,CACH;UAAA,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAChC;QAAA,EAAE,IAAI,CAAC,CACR;SACF;KACF,CAAC,CAAC;IAEH,MAAM,IAAI,GAAG,IAAA,mCAAgB,GAAE,CAAC;IAEhC,yGAAyG;IACzG,sGAAsG;IACtG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAE1B,qEAAqE;IACrE,0HAA0H;IAC1H,4BAA4B,EAAE,CAAC;IAE/B,MAAM,IAAI,GAAG,MAAM,qBAAc,CAAC,cAAc,CAC9C,CAAC,WAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAClC;MAAA,CAAC,wBAAe,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,wBAAe,CACvD;IAAA,EAAE,WAAI,CAAC,QAAQ,CAAC,CACjB,CAAC;IAEF,+EAA+E;IAC/E,MAAM,GAAG,GAAG,qBAAc,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC,CAAC;IAEnE,IAAI,MAAM,GAAG,kCAAkC,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAE1E,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,GAAG,SAAS,CAAC,CAAC;IAEpD,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACxC,KAAK,CAAC,iCAAiC,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;IAC/D,qCAAqC;IACrC,4CAA4C;IAC5C,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;IAE/D,OAAO,iBAAiB,GAAG,MAAM,CAAC;AACpC,CAAC;AAED,SAAS,kCAAkC,CAAC,MAAW,EAAE,IAAY;IACnE,kBAAkB;IAClB,KAAK,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;QACrF,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC;QACzC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED,aAAa;IACb,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC7E,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAE7E,OAAO,IAAI,CAAC;AACd,CAAC;AAED,8BAA8B;AAC9B,yDAAmF;AAA1E,oIAAA,+BAA+B,OAAA;AAAE,gHAAA,WAAW,OAAA", "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport '@expo/metro-runtime';\n\nimport { ServerContainer, ServerContainerRef } from '@react-navigation/native';\nimport * as Font from 'expo-font/build/server';\nimport React from 'react';\nimport ReactDOMServer from 'react-dom/server.node';\n// @ts-expect-error: TODO(@kitten): Define this type (seems to differ from react-native)\nimport { AppRegistry } from 'react-native-web';\n\nimport { getRootComponent } from './getRootComponent';\nimport { ctx } from '../../_ctx';\nimport { ExpoRoot } from '../ExpoRoot';\nimport { Head } from '../head';\n\nconst debug = require('debug')('expo:router:renderStaticContent');\n\nAppRegistry.registerComponent('App', () => ExpoRoot);\n\nfunction resetReactNavigationContexts() {\n  // https://github.com/expo/router/discussions/588\n  // https://github.com/react-navigation/react-navigation/blob/9fe34b445fcb86e5666f61e144007d7540f014fa/packages/elements/src/getNamedContext.tsx#LL3C1-L4C1\n\n  // React Navigation is storing providers in a global, this is fine for the first static render\n  // but subsequent static renders of Stack or Tabs will cause React to throw a warning. To prevent this warning, we'll reset the globals before rendering.\n  const contexts = '__react_navigation__elements_contexts';\n  global[contexts] = new Map<string, React.Context<any>>();\n}\n\nexport async function getStaticContent(location: URL): Promise<string> {\n  const headContext: { helmet?: any } = {};\n\n  const ref = React.createRef<ServerContainerRef>();\n\n  const {\n    // NOTE: The `element` that's returned adds two extra Views and\n    // the seemingly unused `RootTagContext.Provider`.\n    element,\n    getStyleElement,\n  } = AppRegistry.getApplication('App', {\n    initialProps: {\n      location,\n      context: ctx,\n      wrapper: ({ children }: React.ComponentProps<any>) => (\n        <Root>\n          <div id=\"root\">{children}</div>\n        </Root>\n      ),\n    },\n  });\n\n  const Root = getRootComponent();\n\n  // Clear any existing static resources from the global scope to attempt to prevent leaking between pages.\n  // This could break if pages are rendered in parallel or if fonts are loaded outside of the React tree\n  Font.resetServerContext();\n\n  // This MUST be run before `ReactDOMServer.renderToString` to prevent\n  // \"Warning: Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported.\"\n  resetReactNavigationContexts();\n\n  const html = await ReactDOMServer.renderToString(\n    <Head.Provider context={headContext}>\n      <ServerContainer ref={ref}>{element}</ServerContainer>\n    </Head.Provider>\n  );\n\n  // Eval the CSS after the HTML is rendered so that the CSS is in the same order\n  const css = ReactDOMServer.renderToStaticMarkup(getStyleElement());\n\n  let output = mixHeadComponentsWithStaticResults(headContext.helmet, html);\n\n  output = output.replace('</head>', `${css}</head>`);\n\n  const fonts = Font.getServerResources();\n  debug(`Pushing static fonts: (count: ${fonts.length})`, fonts);\n  // debug('Push static fonts:', fonts)\n  // Inject static fonts loaded with expo-font\n  output = output.replace('</head>', `${fonts.join('')}</head>`);\n\n  return '<!DOCTYPE html>' + output;\n}\n\nfunction mixHeadComponentsWithStaticResults(helmet: any, html: string) {\n  // Head components\n  for (const key of ['title', 'priority', 'meta', 'link', 'script', 'style'].reverse()) {\n    const result = helmet?.[key]?.toString();\n    if (result) {\n      html = html.replace('<head>', `<head>${result}`);\n    }\n  }\n\n  // attributes\n  html = html.replace('<html ', `<html ${helmet?.htmlAttributes.toString()} `);\n  html = html.replace('<body ', `<body ${helmet?.bodyAttributes.toString()} `);\n\n  return html;\n}\n\n// Re-export for use in server\nexport { getBuildTimeServerManifestAsync, getManifest } from './getServerManifest';\n"]}