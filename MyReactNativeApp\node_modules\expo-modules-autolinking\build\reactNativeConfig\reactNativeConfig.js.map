{"version": 3, "file": "reactNativeConfig.js", "sourceRoot": "", "sources": ["../../src/reactNativeConfig/reactNativeConfig.ts"], "names": [], "mappings": ";;;;;AA6BA,oEAwDC;AAKD,4DA+BC;AAoBD,oEA8CC;AAED,0EAUC;AAED,oEA6BC;AAtOD,2DAA6B;AAC7B,gDAAwB;AACxB,gEAAuC;AAEvC,gDAA8D;AAC9D,4CAA+C;AAE/C,uDAI2B;AAC3B,qCAA2C;AAC3C,+CAAoE;AASpE,kDAAkE;AAElE,MAAM,wCAAwC,GAAG,wBAAwB,CAAC;AAE1E;;GAEG;AACI,KAAK,UAAU,4BAA4B,CAAC,EACjD,QAAQ,EACR,WAAW,EACX,WAAW,GACY;IACvB,MAAM,aAAa,GAAG,MAAM,IAAA,wBAAe,EAAmC,WAAW,CAAC,CAAC;IAC3F,MAAM,eAAe,GAAG;QACtB,GAAG,CAAC,MAAM,wBAAwB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC7D,GAAG,+BAA+B,CAAC,aAAa,CAAC;KAClD,CAAC;IAEF,yHAAyH;IACzH,8IAA8I;IAC9I,mHAAmH;IACnH,8IAA8I;IAC9I,sHAAsH;IACtH,MAAM,wBAAwB,GAC5B,QAAQ,KAAK,SAAS;QACtB,CAAC,MAAM,8BAA8B,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC,CAAC,2BAA2B,IAAI,eAAe,CAAC,CAAC;IAEpD,IAAI,wBAAwB,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAG,+BAA+B,CAAC,WAAW,CAAC,CAAC;QACpE,IAAI,cAAc,EAAE,CAAC;YACnB,eAAe,CAAC,2BAA2B,CAAC,GAAG,cAAc,CAAC;QAChE,CAAC;IACH,CAAC;IAED,6EAA6E;IAC7E,gFAAgF;IAChF,oCAAoC;IACpC,IAAI,eAAuB,CAAC;IAC5B,IAAI,CAAC;QACH,eAAe,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC;IACvE,CAAC;IAAC,MAAM,CAAC;QACP,eAAe,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,GAAG,CACzC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,EAAE;QAChE,MAAM,MAAM,GAAG,MAAM,4BAA4B,CAAC,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;QAC9F,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,CACH,CAAC;IACF,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAC1C,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,IAAI,IAAI,CAEtD,CACF,CAAC;IACF,MAAM,WAAW,GAAG,MAAM,4BAA4B,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC9E,OAAO;QACL,IAAI,EAAE,WAAW;QACjB,eAAe;QACf,YAAY,EAAE,iBAAiB;QAC/B,OAAO,EAAE,WAAW;KACrB,CAAC;AACJ,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,wBAAwB,CAC5C,WAAmB,EACnB,WAAqB;IAErB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAClG,MAAM,YAAY,GAAG;QACnB,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC;QAC9C,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,IAAI,EAAE,CAAC;KAClD,CAAC;IAEF,MAAM,OAAO,GAA2B,EAAE,CAAC;IAC3C,wGAAwG;IACxG,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;IAE3C,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;QAChC,KAAK,MAAM,UAAU,IAAI,aAAa,EAAE,CAAC;YACvC,MAAM,iBAAiB,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YACzE,IAAI,MAAM,IAAA,2BAAe,EAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC7C,MAAM,WAAW,GAAG,cAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;gBAE5B,MAAM,wBAAwB,GAAG,IAAA,8BAAsB,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC3E,IAAI,wBAAwB,EAAE,CAAC;oBAC7B,aAAa,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBAC9C,CAAC;gBACD,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAS,+BAA+B,CACtC,aAAsD;IAEtD,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC;QACjC,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,OAAO,GAA2B,EAAE,CAAC;IAC3C,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;QACxE,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC;QAC9B,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAEM,KAAK,UAAU,4BAA4B,CAChD,QAA2B,EAC3B,IAAY,EACZ,WAAmB,EACnB,aAAsD;IAEtD,MAAM,aAAa,GAAG,MAAM,IAAA,wBAAe,EAAmC,WAAW,CAAC,CAAC;IAC3F,MAAM,iBAAiB,GAAG;QACxB,GAAG,aAAa,EAAE,UAAU;QAC5B,GAAG,aAAa,EAAE,YAAY,EAAE,CAAC,IAAI,CAAC;KACvC,CAAC;IAEF,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC3D,8DAA8D;QAC9D,sCAAsC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,oBAAoB,EAAE,CAAC;QAC7D,gFAAgF;QAChF,sEAAsE;QACtE,gDAAgD;QAChD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,YAAY,GAAG,MAAM,IAAA,yDAAuC,EAC1D,WAAW,EACX,iBAAiB,CAAC,SAAS,EAAE,OAAO,CACrC,CAAC;IACJ,CAAC;SAAM,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;QAC9B,YAAY,GAAG,MAAM,IAAA,iDAAmC,EACtD,WAAW,EACX,iBAAiB,CAAC,SAAS,EAAE,GAAG,CACjC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO;QACL,IAAI,EAAE,WAAW;QACjB,IAAI;QACJ,SAAS,EAAE;YACT,CAAC,QAAQ,CAAC,EAAE,YAAY;SACzB;KACF,CAAC;AACJ,CAAC;AAED,SAAgB,+BAA+B,CAAC,WAAmB;IACjE,MAAM,eAAe,GAAG,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAC7E,MAAM,cAAc,GAAG,sBAAW,CAAC,MAAM,CACvC,eAAe,IAAI,WAAW,EAC9B,wCAAwC,CACzC,CAAC;IACF,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,cAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IACtC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,4BAA4B,CAChD,WAAmB,EACnB,QAA2B;IAE3B,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACrD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAA,4CAA0B,EAAC,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QAChG,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACvC,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,IAAA,uCAAqB,EAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9E,OAAO;YACL,OAAO,EAAE;gBACP,WAAW,EAAE,WAAW,IAAI,EAAE;gBAC9B,SAAS,EAAE,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC;aAC7C;SACF,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;QACvB,OAAO;YACL,GAAG,EAAE;gBACH,SAAS,EAAE,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;aACzC;SACF,CAAC;IACJ,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,8BAA8B,CAAC,WAAmB;IAC/D,OAAO,CACL,CAAC,MAAM,IAAA,oCAA0B,EAC/B,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,EACjC,wCAAwC,CACzC,CAAC,KAAK,MAAM,CACd,CAAC;AACJ,CAAC", "sourcesContent": ["import fs from 'fs/promises';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { getIsolatedModulesPath } from '../autolinking/utils';\nimport { fileExistsAsync } from '../fileUtils';\nimport type { SupportedPlatform } from '../types';\nimport {\n  findGradleAndManifestAsync,\n  parsePackageNameAsync,\n  resolveDependencyConfigImplAndroidAsync,\n} from './androidResolver';\nimport { loadConfigAsync } from './config';\nimport { resolveDependencyConfigImplIosAsync } from './iosResolver';\nimport type {\n  RNConfigCommandOptions,\n  RNConfigDependency,\n  RNConfigReactNativeAppProjectConfig,\n  RNConfigReactNativeLibraryConfig,\n  RNConfigReactNativeProjectConfig,\n  RNConfigResult,\n} from './reactNativeConfig.types';\nimport { resolveGradlePropertyAsync } from '../platforms/android';\n\nconst EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY = 'expo.edgeToEdgeEnabled';\n\n/**\n * Create config for react-native core autolinking.\n */\nexport async function createReactNativeConfigAsync({\n  platform,\n  projectRoot,\n  searchPaths,\n}: RNConfigCommandOptions): Promise<RNConfigResult> {\n  const projectConfig = await loadConfigAsync<RNConfigReactNativeProjectConfig>(projectRoot);\n  const dependencyRoots = {\n    ...(await findDependencyRootsAsync(projectRoot, searchPaths)),\n    ...findProjectLocalDependencyRoots(projectConfig),\n  };\n\n  // For Expo SDK 53 onwards, `react-native-edge-to-edge` is a transitive dependency of every expo project. Unless the user\n  // has also included it as a project dependency, we have to autolink it manually (transitive non-expo module dependencies are not autolinked).\n  // There are two reasons why we don't want to autolink `edge-to-edge` when `edgeToEdge` property is set to `false`:\n  // 1. `react-native-is-edge-to-edge` tries to check if the `edge-to-edge` turbomodule is present to determine whether edge-to-edge is enabled.\n  // 2. `react-native-edge-to-edge` applies edge-to-edge in `onHostResume` and has no property to disable this behavior.\n  const shouldAutolinkEdgeToEdge =\n    platform === 'android' &&\n    (await resolveGradleEdgeToEdgeEnabled(projectRoot)) &&\n    !('react-native-edge-to-edge' in dependencyRoots);\n\n  if (shouldAutolinkEdgeToEdge) {\n    const edgeToEdgeRoot = resolveEdgeToEdgeDependencyRoot(projectRoot);\n    if (edgeToEdgeRoot) {\n      dependencyRoots['react-native-edge-to-edge'] = edgeToEdgeRoot;\n    }\n  }\n\n  // NOTE(@kitten): If this isn't resolved to be the realpath and is a symlink,\n  // the Cocoapods resolution will detect path mismatches and generate nonsensical\n  // relative paths that won't resolve\n  let reactNativePath: string;\n  try {\n    reactNativePath = await fs.realpath(dependencyRoots['react-native']);\n  } catch {\n    reactNativePath = dependencyRoots['react-native'];\n  }\n\n  const dependencyConfigs = await Promise.all(\n    Object.entries(dependencyRoots).map(async ([name, packageRoot]) => {\n      const config = await resolveDependencyConfigAsync(platform, name, packageRoot, projectConfig);\n      return [name, config];\n    })\n  );\n  const dependencyResults = Object.fromEntries<RNConfigDependency>(\n    dependencyConfigs.filter(([, config]) => config != null) as Iterable<\n      [string, RNConfigDependency]\n    >\n  );\n  const projectData = await resolveAppProjectConfigAsync(projectRoot, platform);\n  return {\n    root: projectRoot,\n    reactNativePath,\n    dependencies: dependencyResults,\n    project: projectData,\n  };\n}\n\n/**\n * Find all dependencies and their directories from the project.\n */\nexport async function findDependencyRootsAsync(\n  projectRoot: string,\n  searchPaths: string[]\n): Promise<Record<string, string>> {\n  const packageJson = JSON.parse(await fs.readFile(path.join(projectRoot, 'package.json'), 'utf8'));\n  const dependencies = [\n    ...Object.keys(packageJson.dependencies ?? {}),\n    ...Object.keys(packageJson.devDependencies ?? {}),\n  ];\n\n  const results: Record<string, string> = {};\n  // `searchPathSet` can be mutated to discover all \"isolated modules groups\", when using isolated modules\n  const searchPathSet = new Set(searchPaths);\n\n  for (const name of dependencies) {\n    for (const searchPath of searchPathSet) {\n      const packageConfigPath = path.resolve(searchPath, name, 'package.json');\n      if (await fileExistsAsync(packageConfigPath)) {\n        const packageRoot = path.dirname(packageConfigPath);\n        results[name] = packageRoot;\n\n        const maybeIsolatedModulesPath = getIsolatedModulesPath(packageRoot, name);\n        if (maybeIsolatedModulesPath) {\n          searchPathSet.add(maybeIsolatedModulesPath);\n        }\n        break;\n      }\n    }\n  }\n\n  return results;\n}\n\n/**\n * Find local dependencies that specified in the `react-native.config.js` file.\n */\nfunction findProjectLocalDependencyRoots(\n  projectConfig: RNConfigReactNativeProjectConfig | null\n): Record<string, string> {\n  if (!projectConfig?.dependencies) {\n    return {};\n  }\n  const results: Record<string, string> = {};\n  for (const [name, config] of Object.entries(projectConfig.dependencies)) {\n    if (typeof config.root === 'string') {\n      results[name] = config.root;\n    }\n  }\n  return results;\n}\n\nexport async function resolveDependencyConfigAsync(\n  platform: SupportedPlatform,\n  name: string,\n  packageRoot: string,\n  projectConfig: RNConfigReactNativeProjectConfig | null\n): Promise<RNConfigDependency | null> {\n  const libraryConfig = await loadConfigAsync<RNConfigReactNativeLibraryConfig>(packageRoot);\n  const reactNativeConfig = {\n    ...libraryConfig?.dependency,\n    ...projectConfig?.dependencies?.[name],\n  };\n\n  if (Object.keys(libraryConfig?.platforms ?? {}).length > 0) {\n    // Package defines platforms would be a platform host package.\n    // The rnc-cli will skip this package.\n    return null;\n  }\n  if (name === 'react-native' || name === 'react-native-macos') {\n    // Starting from version 0.76, the `react-native` package only defines platforms\n    // when @react-native-community/cli-platform-android/ios is installed.\n    // Therefore, we need to manually filter it out.\n    return null;\n  }\n\n  let platformData = null;\n  if (platform === 'android') {\n    platformData = await resolveDependencyConfigImplAndroidAsync(\n      packageRoot,\n      reactNativeConfig.platforms?.android\n    );\n  } else if (platform === 'ios') {\n    platformData = await resolveDependencyConfigImplIosAsync(\n      packageRoot,\n      reactNativeConfig.platforms?.ios\n    );\n  }\n  if (!platformData) {\n    return null;\n  }\n  return {\n    root: packageRoot,\n    name,\n    platforms: {\n      [platform]: platformData,\n    },\n  };\n}\n\nexport function resolveEdgeToEdgeDependencyRoot(projectRoot: string): string | null {\n  const expoPackageRoot = resolveFrom.silent(projectRoot, 'expo/package.json');\n  const edgeToEdgePath = resolveFrom.silent(\n    expoPackageRoot ?? projectRoot,\n    'react-native-edge-to-edge/package.json'\n  );\n  if (edgeToEdgePath) {\n    return path.dirname(edgeToEdgePath);\n  }\n  return null;\n}\n\nexport async function resolveAppProjectConfigAsync(\n  projectRoot: string,\n  platform: SupportedPlatform\n): Promise<RNConfigReactNativeAppProjectConfig> {\n  if (platform === 'android') {\n    const androidDir = path.join(projectRoot, 'android');\n    const { gradle, manifest } = await findGradleAndManifestAsync({ androidDir, isLibrary: false });\n    if (gradle == null || manifest == null) {\n      return {};\n    }\n    const packageName = await parsePackageNameAsync(androidDir, manifest, gradle);\n\n    return {\n      android: {\n        packageName: packageName ?? '',\n        sourceDir: path.join(projectRoot, 'android'),\n      },\n    };\n  }\n\n  if (platform === 'ios') {\n    return {\n      ios: {\n        sourceDir: path.join(projectRoot, 'ios'),\n      },\n    };\n  }\n\n  return {};\n}\n\n/**\n * Resolve the `expo.edgeToEdgeEnabled` property from the `gradle.properties` file.\n */\nasync function resolveGradleEdgeToEdgeEnabled(projectRoot: string): Promise<boolean> {\n  return (\n    (await resolveGradlePropertyAsync(\n      path.join(projectRoot, 'android'),\n      EDGE_TO_EDGE_ENABLED_GRADLE_PROPERTY_KEY\n    )) === 'true'\n  );\n}\n"]}